<template>
    <a-spin :spinning="loading">
        <div class="device_detail">
            <div class="flex mb-4 justify-between">
                <div class="text-base leading-8 go-box flex items-center">
                    <span
                        class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                        @click="goRouter"
                        >{{ selectSupplierInfoCar?.name || '-' }}</span
                    >

                    <iconSvg
                        name="rightIcon"
                        class="more-icon text-primary-text dark:text-80-dark"
                    />
                    <!-- 站点详情 -->
                    <span class="ml-1">{{
                        projectData?.projectName || '-'
                    }}</span>
                </div>
            </div>
            <div class="flex detail-info mb-3 p-4 items-center h-ful">
                <div class="flex-1 w-0">
                    <div class="flex gap-x-5">
                        <div
                            class="bg-ff dark:bg-ff-dark rounded overflow-hidden"
                            style="width: 333px; height: 250px"
                        >
                            <img
                                :src="defaultImg"
                                class="w-full h-full"
                                alt=""
                                srcset=""
                            />
                        </div>
                        <main-info
                            :project-data="projectData"
                            :customer-detail="customerDetail"
                        />
                    </div>
                </div>
                <a-divider type="vertical" class="m-0 h-52" />
                <div
                    class="pie flex items-center justify-between px-3"
                    style="width: 530px"
                >
                    <div class="flex-1">
                        <statusPieChart v-model:data="statusData" />
                    </div>
                    <div class="flex-1 text-center">
                        <socPieChart v-model:data="SOCData" />
                    </div>
                </div>
            </div>
            <div class="flex gap-x-3 w-full mb-5">
                <!-- 充放电 -->
                <div
                    class="w-1/4 tabs-content rounded-lg"
                    :class="activeKey === '1' ? 'active' : ''"
                    @click="changeTab('1')"
                >
                    <div
                        class="px-4 pt-3 pb-4 bg-ff dark:bg-ff-dark rounded-lg cursor-pointer"
                    >
                        <div
                            class="h-12 px-5 py-2 rounded text-sm font-medium leading-12 mb-2 flex items-end text-title dark:text-title-dark"
                            style="background: rgba(149, 158, 195, 0.1)"
                        >
                            <div class="leading-6 text-base">
                                {{ $t("Yesterday's active devices") }}
                            </div>
                            <div class="text-3.5xl leading-8 font-bold ml-3">
                                {{ activeData?.yesterdayActiveCount }}
                            </div>
                            <div class="text-xs leading-5 ml-0.5">
                                {{ $t('tai') }}
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center px-5 mb-2.5"
                        >
                            <div class="flex-1 text-left leading-6 h-6">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        activeData?.beforeYesterdayActiveCount ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        activeData.comparedBeforeYesterdayPercent
                                    "
                                />
                            </div>
                        </div>
                        <a-divider class="m-0" />
                        <div
                            class="flex justify-between items-center px-5 mt-3.5 leading-4"
                        >
                            <div class="flex-1 text-left flex">
                                <div class="flex-1">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('Total devices') }}
                                        {{
                                            $t('tai')
                                                ? '(' + $t('tai') + ')'
                                                : ''
                                        }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            projectData?.bmsSummaryInfo
                                                .totalDevices
                                        }}
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('Loading Energy') }}({{
                                            alternateUnits(
                                                projectData?.bmsSummaryInfo
                                                    .totalEnergy || 0,
                                                1000
                                            )
                                                ? 'MWh'
                                                : 'kWh'
                                        }})
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            unitConversion(
                                                projectData?.bmsSummaryInfo
                                                    .totalEnergy || 0,
                                                1000
                                            )
                                        }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="w-1/2 tabs-content rounded-lg"
                    :class="activeKey === '2' ? 'active' : ''"
                    @click="changeTab('2')"
                >
                    <div
                        class="px-4 pt-3 pb-4 bg-ff dark:bg-ff-dark rounded-lg cursor-pointer"
                    >
                        <div
                            class="charge-title flex justify-between text-title dark:text-title-dark"
                        >
                            <div
                                class="charge-title-l h-12 pl-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end"
                            >
                                <div class="leading-6 text-base">
                                    {{ $t("Yesterday's charge") }}
                                </div>
                                <div
                                    class="text-3.5xl leading-8 font-bold ml-4.5"
                                >
                                    {{
                                        chargeStatisticsData.yesterdayChargeDur
                                    }}
                                </div>
                                <div class="text-xs leading-5 ml-0.5">
                                    {{ 'h' }}
                                </div>
                            </div>
                            <div
                                class="charge-title-r h-12 pr-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end justify-end"
                            >
                                <div class="leading-6 text-base">
                                    {{ $t("Yesterday's discharge") }}
                                </div>
                                <div
                                    class="text-3.5xl leading-8 font-bold ml-4.5"
                                >
                                    {{
                                        chargeStatisticsData.yesterdayDischargeDur
                                    }}
                                </div>
                                <div class="text-xs leading-5 ml-0.5">
                                    {{ 'h' }}
                                </div>
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center px-3"
                            style="line-height: 42px"
                        >
                            <div class="flex-1 text-left">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        chargeStatisticsData?.beforeYesterdayChargeDur ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        chargeStatisticsData.comparedChargePercent
                                    "
                                />
                            </div>
                            <div class="flex-1 text-right">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        chargeStatisticsData?.beforeYesterdayDischargeDur ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        chargeStatisticsData.comparedDischargePercent
                                    "
                                />
                            </div>
                        </div>
                        <a-divider class="m-0" />
                        <div
                            class="flex justify-between items-center px-3 mt-3.5 leading-4"
                        >
                            <div class="flex-1 text-left flex">
                                <div>
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_yueleiji') }}(h)
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.currentMonthChargeDur
                                        }}
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_zongji') }}(h)
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.totalChargeDur
                                        }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex-1 text-right flex justify-end">
                                <div>
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_yueleiji') }}(h)
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.currentMonthDischargeDur
                                        }}
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_zongji') }}(h)
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.totalDischargeDur
                                        }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="w-1/4 tabs-content rounded-lg"
                    :class="activeKey == '3' ? 'active' : ''"
                    @click="changeTab('3')"
                >
                    <div
                        class="px-4 pt-3 pb-4 bg-ff dark:bg-ff-dark rounded-lg cursor-pointer"
                    >
                        <div
                            class="rounded text-sm h-12 leading-12 font-medium flex justify-between items-center"
                        >
                            <div class="text-title dark:text-title-dark">
                                {{ $t('当前异常') }}：<span
                                    class="text-2.5xl font-bold ml-1"
                                >
                                    {{ alarmData.unDisposedQuantity }}</span
                                ><span class="text-xs">{{
                                    $t('common_ge')
                                }}</span>
                            </div>
                            <i
                                class="iconfont icon-a-ica-dianchi-guzhangbeifen16 text-2xl"
                                style="color: rgb(253, 11, 11)"
                            ></i>
                        </div>
                        <div class="flex gap-x-5 mt-4 leading-4 text-center">
                            <div
                                class="rounded-lg bg-f5 dark:bg-4c6179 py-5 flex-1"
                            >
                                <div
                                    class="text-secondar-text dark:text-60-dark mb-2"
                                >
                                    {{ $t('今日新增') }}
                                </div>
                                <div
                                    class="font-medium text-base leading-4 text-primary-text dark:text-80-dark"
                                >
                                    {{ alarmData.todayQuantity }}
                                </div>
                            </div>
                            <div
                                class="rounded-lg bg-f5 dark:bg-4c6179 py-5 flex-1"
                            >
                                <div
                                    class="text-secondar-text dark:text-60-dark mb-2"
                                >
                                    {{ $t('七日新增') }}
                                </div>
                                <div
                                    class="font-medium text-base leading-4 text-primary-text dark:text-80-dark"
                                >
                                    {{ alarmData.sevenDayQuantity }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="topTabs">
                <a-tabs class="w-full tabs pt-1" :activeKey="activeKey">
                    <a-tab-pane key="1" tab="a">
                        <div class="p-4 rounded-lg bg-ff dark:bg-ff-dark mb-3">
                            <div
                                class="flex mb-2.5 items-center justify-between"
                            >
                                <div
                                    class="text-title dark:text-title-dark w-56"
                                >
                                    <div
                                        class="cursor-pointer toogle-icon overflow-hidden overflow-ellipsis whitespace-nowrap"
                                        v-popover="popoverRef"
                                        v-click-outside="onClickOutside"
                                        v-if="
                                            customerDetail.projectId ||
                                            customerDetail.customerId
                                        "
                                    >
                                        <iconSvg
                                            class="w-5 h-5 align-middle mr-1"
                                            name="toggle"
                                            className="icon-search"
                                        />
                                        <span class="align-middle">
                                            {{
                                                formatterDeviceName(
                                                    currentDeviceName
                                                )
                                            }}
                                        </span>
                                    </div>
                                    <el-popover
                                        ref="popoverRef"
                                        trigger="click"
                                        title=""
                                        virtual-triggering
                                        persistent
                                        :popper-style="
                                            customerDetail.projectId
                                                ? 'width:420px'
                                                : 'width:525px'
                                        "
                                        placement="bottom-start"
                                        @show="onOpenPopover"
                                    >
                                        <div>
                                            <div class="flex search-group">
                                                <el-input
                                                    class="flex-1"
                                                    v-model="
                                                        deviceSearchKeyword
                                                    "
                                                    @input="onSearchDevice"
                                                ></el-input>
                                                <el-button
                                                    @click="onEditName"
                                                    round
                                                    class="ml-3"
                                                    v-if="
                                                        !customerDetail.projectId
                                                    "
                                                >
                                                    <span>{{
                                                        $t('Edit')
                                                    }}</span>
                                                    <iconSvg
                                                        class="w-4 h-4 ml-1"
                                                        name="edit"
                                                        className="icon-search w-4 h-4 ml-1"
                                                    />
                                                </el-button>
                                            </div>
                                            <div
                                                class="overflow-hidden"
                                                style="
                                                    height: 240px;
                                                    width: 100%;
                                                "
                                            >
                                                <div
                                                    class="w-full h-full overflow-y-auto"
                                                >
                                                    <div
                                                        class="flex items-center justify-between leading-10 cursor-pointer text-title dark:text-title-dark car-item"
                                                        :class="
                                                            item.sn ==
                                                            currentDeviceSn
                                                                ? 'active'
                                                                : ''
                                                        "
                                                        v-for="item in equipmentData"
                                                        :key="item"
                                                        @click="
                                                            onSelectDevice(item)
                                                        "
                                                    >
                                                        <div
                                                            class="flex-1 w-0 overflow-hidden overflow-ellipsis whitespace-nowrap pl-3 pr-2 opacity-80"
                                                            :title="`${item.sn}`"
                                                        >
                                                            {{
                                                                formatterDeviceName(
                                                                    item.remarkName
                                                                )
                                                            }}
                                                            ({{
                                                                $t('Device No')
                                                            }}：{{ item.sn }})
                                                        </div>
                                                        <div
                                                            class="relative"
                                                            :style="
                                                                customerDetail.projectId
                                                                    ? 'width: 118px;padding-right: 0;'
                                                                    : 'width: 158px;padding-right: 40px;'
                                                            "
                                                        >
                                                            <div
                                                                class="flex items-center gap-x-1"
                                                                :style="{
                                                                    color: getState(
                                                                        item.status,
                                                                        'power'
                                                                    ).color,
                                                                }"
                                                            >
                                                                <i
                                                                    class="w-6 h-6 text-2xl leading-6"
                                                                    :class="[
                                                                        'iconfont',
                                                                        getState(
                                                                            item.status,
                                                                            'power'
                                                                        ).icon,
                                                                    ]"
                                                                ></i>
                                                                <span
                                                                    class="leading-6"
                                                                    >{{
                                                                        $t(
                                                                            getState(
                                                                                item.status,
                                                                                'power'
                                                                            )
                                                                                .label
                                                                        )
                                                                    }}</span
                                                                >
                                                            </div>
                                                            <div
                                                                class="w-6 h-6 flex justify-center items-center absolute right-0 top-0"
                                                            >
                                                                <iconSvg
                                                                    v-show="
                                                                        item.sn ==
                                                                        currentDeviceSn
                                                                    "
                                                                    name="checked"
                                                                    class="w-6 h-6"
                                                                    className="tree-svg-box"
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                                <div class="">
                                    <el-tabs
                                        v-model="activeName"
                                        @tab-change="handleTabChange"
                                    >
                                        <template #default>
                                            <el-tab-pane
                                                :label="$t('Basic Info')"
                                                name="a"
                                            >
                                            </el-tab-pane>
                                            <el-tab-pane
                                                :label="$t('实时状态')"
                                                name="b"
                                            >
                                            </el-tab-pane>
                                            <el-tab-pane
                                                :label="$t('历史数据')"
                                                name="c"
                                            >
                                            </el-tab-pane>
                                        </template>
                                    </el-tabs>
                                </div>
                                <div class="w-56">
                                    <div class="flex justify-end gap-x-3">
                                        <!-- <el-popconfirm
                                            :title="
                                                realData?.lockSta == 1
                                                    ? $t('是否确认停用')
                                                    : $t('是否确认启用')
                                            "
                                            @confirm="OffDevice"
                                            :confirm-button-text="
                                                $t('common_shi')
                                            "
                                            :cancel-button-text="
                                                $t('common_fou')
                                            "
                                            width="240"
                                        >
                                            <template #reference>
                                                <el-button
                                                    plain
                                                    round
                                                    class="btn-hover"
                                                >
                                                    <span>{{
                                                        $t('Deactivate')
                                                    }}</span>
                                                    <span class="icon-box ml-1">
                                                        <iconSvg
                                                            name="off"
                                                            class="icon-default"
                                                        />
                                                    </span>
                                                </el-button>
                                            </template>
                                        </el-popconfirm> -->
                                        <!--  @click="handleRefresh" -->
                                        <el-button
                                            plain
                                            round
                                            class="btn-hover"
                                            @click="handleRefresh"
                                        >
                                            <span>{{ $t('Refresh') }}</span>
                                            <span class="icon-box ml-1">
                                                <iconSvg
                                                    name="refresh"
                                                    class="icon-default"
                                                />
                                            </span>
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                            <div v-if="activeName == 'a'">
                                <div class="device-box">
                                    <div class="device-img">
                                        <car-img
                                            :vehicleType="
                                                activeDeviceInfo?.vehicleType
                                            "
                                        />
                                    </div>
                                    <el-divider
                                        direction="vertical"
                                        style="
                                            height: 325px;
                                            border-color: var(--border);
                                        "
                                    />
                                    <div class="device-info">
                                        <div>
                                            <basic-info :data="basicInfoData" />
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-if="activeName == 'a'"
                                    class="p-4 pt-2 rounded-lg mb-3 border border-border dark:border-border-dark"
                                >
                                    <!-- <div
                                        class="mb-2 text-title dark:text-title-dark"
                                    >
                                        {{ $t('statistical_analysis') }}
                                    </div> -->
                                    <div
                                        class="text-title dark:text-title-dark"
                                    >
                                        <el-tabs
                                            v-model="totalAnalysisName"
                                            @tab-change="
                                                handleTabChangeTotalAnalysi
                                            "
                                            class="mb-3"
                                        >
                                            <template #default>
                                                <el-tab-pane
                                                    :label="
                                                        $t(
                                                            'Device status statistics'
                                                        )
                                                    "
                                                    name="a"
                                                >
                                                </el-tab-pane>
                                                <el-tab-pane
                                                    :label="
                                                        $t(
                                                            'charging_behavior_analysis'
                                                        )
                                                    "
                                                    name="b"
                                                >
                                                </el-tab-pane>
                                            </template>
                                        </el-tabs>

                                        <div
                                            class="relative bg-f5 dark:bg-4c6179 p-3 rounded-lg"
                                            style="min-height: 456px"
                                            v-if="totalAnalysisName == 'a'"
                                        >
                                            <div
                                                class="flex items-center justify-end absolute right-3 top-3"
                                                style="z-index: 48"
                                            >
                                                <date-search
                                                    :info="{
                                                        periodOptions: [
                                                            'day',
                                                            'month',
                                                        ],
                                                        datePickerType:
                                                            'minute',
                                                        dayRangeLen: 30,
                                                        defaultDayRangeLen: 30,
                                                        minDate: moment(
                                                            basicInfoData
                                                                ?.bmsInfo
                                                                ?.activeTime
                                                        ).format('YYYY-MM-DD'),
                                                    }"
                                                    @onChange="
                                                        onChargeTotalDateChange
                                                    "
                                                    v-model:dateSelect="
                                                        chargeTotalDateType
                                                    "
                                                />
                                            </div>
                                            <el-radio-group
                                                v-model="deviceStatusActiveName"
                                                @change="
                                                    handleDeviceStatusTabChange
                                                "
                                            >
                                                <el-radio-button value="a">{{
                                                    $t(
                                                        'station_chongfangdianliang'
                                                    )
                                                }}</el-radio-button>
                                                <el-radio-button value="b">{{
                                                    $t('runTime')
                                                }}</el-radio-button>
                                            </el-radio-group>
                                            <div class="">
                                                <div
                                                    v-if="
                                                        deviceStatusActiveName ==
                                                        'a'
                                                    "
                                                    id="chargeTotalByDate"
                                                    style="
                                                        width: 100%;
                                                        height: 400px;
                                                    "
                                                ></div>

                                                <div
                                                    v-if="
                                                        deviceStatusActiveName ==
                                                        'b'
                                                    "
                                                    id="runningDuration"
                                                    style="
                                                        width: 100%;
                                                        height: 400px;
                                                    "
                                                ></div>
                                            </div>
                                        </div>
                                        <div
                                            class="relative bg-f5 dark:bg-4c6179 p-3 rounded-lg"
                                            style="min-height: 456px"
                                            v-if="totalAnalysisName == 'b'"
                                        >
                                            <div
                                                class="flex justify-between items-center"
                                            >
                                                <div
                                                    class="text-title dark:text-title-dark"
                                                ></div>
                                                <div
                                                    class="absolute right-3 top-3"
                                                >
                                                    <date-search
                                                        :info="{
                                                            periodOptions: [
                                                                // 'day',
                                                            ],
                                                            datePickerType:
                                                                'day',
                                                            dayRangeLen: 30,
                                                            defaultDayRangeLen: 30,
                                                        }"
                                                        @onChange="
                                                            handleChargeAnalysisDateChange
                                                        "
                                                        v-model:dateSelect="
                                                            chargeAnalysisDateRange
                                                        "
                                                    />
                                                </div>
                                            </div>
                                            <el-radio-group
                                                v-model="analysisActiveName"
                                                @change="
                                                    handleAnalysisTabChange
                                                "
                                            >
                                                <el-radio-button value="a">{{
                                                    $t(
                                                        'charging_period_distribution'
                                                    )
                                                }}</el-radio-button>
                                                <el-radio-button value="b">{{
                                                    $t(
                                                        'SOC_distribution_at_the_start_of_charging'
                                                    )
                                                }}</el-radio-button>
                                                <el-radio-button value="c">{{
                                                    $t(
                                                        'single_charge_capacity_distribution'
                                                    )
                                                }}</el-radio-button>
                                            </el-radio-group>
                                            <!-- p-3 border border-border dark:border-border-dark -->
                                            <div class="rounded w-full">
                                                <div
                                                    v-if="
                                                        analysisActiveName ==
                                                        'a'
                                                    "
                                                >
                                                    <div
                                                        id="chargeTimeDistribution"
                                                        style="
                                                            width: 100%;
                                                            height: 400px;
                                                        "
                                                    ></div>
                                                </div>

                                                <div
                                                    v-if="
                                                        analysisActiveName ==
                                                        'b'
                                                    "
                                                >
                                                    <div
                                                        id="chargeStartSocDistribution"
                                                        style="
                                                            width: 100%;
                                                            height: 400px;
                                                        "
                                                    ></div>
                                                </div>

                                                <div
                                                    v-if="
                                                        analysisActiveName ==
                                                        'c'
                                                    "
                                                >
                                                    <div
                                                        id="chargeCapacityDistribution"
                                                        style="
                                                            width: 100%;
                                                            height: 400px;
                                                        "
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="activeName == 'b'" class="w-full">
                                <div class="device-box">
                                    <div class="device-img">
                                        <car-img
                                            :vehicleType="
                                                activeDeviceInfo?.vehicleType
                                            "
                                        />
                                    </div>
                                    <el-divider
                                        direction="vertical"
                                        style="
                                            height: 325px;
                                            border-color: var(--border);
                                        "
                                    />
                                    <div class="device-info">
                                        <div>
                                            <real-time-info
                                                :data="{
                                                    ...realData,
                                                    status: basicInfoData
                                                        ?.bmsInfo.status,
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="border border-border dark:border-border-dark rounded"
                                >
                                    <div
                                        class="text-title dark:text-title-dark rounded px-4"
                                    >
                                        <!-- <div>电芯分析</div> -->
                                        <el-tabs
                                            v-model="activeDistributionName"
                                            @tab-change="
                                                handleActiveDistributionTabChange
                                            "
                                        >
                                            <el-tab-pane
                                                :label="$t('voltage_details')"
                                                name="a"
                                            >
                                            </el-tab-pane>
                                            <el-tab-pane
                                                :label="$t('temp_details')"
                                                name="b"
                                            >
                                            </el-tab-pane>
                                        </el-tabs>
                                        <div
                                            id="thermalDistribution"
                                            style="width: 100%; height: 200px"
                                        ></div>
                                        <div
                                            class="flex gap-x-5 text-title dark:text-title-dark mt-5 px-4 pb-4"
                                        >
                                            <div class="flex-1">
                                                <div class="">
                                                    {{ $t('Historical range') }}
                                                </div>
                                                <div class="rounded">
                                                    <div
                                                        id="analysisChartJ"
                                                        style="
                                                            width: 100%;
                                                            height: 390px;
                                                        "
                                                    ></div>
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <div class="">
                                                    {{
                                                        $t(
                                                            'Historical variance'
                                                        )
                                                    }}
                                                </div>
                                                <div class="rounded">
                                                    <div
                                                        id="analysisChartF"
                                                        style="
                                                            width: 100%;
                                                            height: 390px;
                                                        "
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="activeName == 'c'">
                                <el-tabs
                                    v-model="activeHistoryName"
                                    @tab-change="handleActiveHistoryTabChange"
                                >
                                    <template #default>
                                        <el-tab-pane
                                            :label="$t('operation_data')"
                                            name="a"
                                        >
                                        </el-tab-pane>
                                        <el-tab-pane
                                            :label="$t('device_data')"
                                            name="b"
                                        >
                                        </el-tab-pane>
                                    </template>
                                </el-tabs>
                                <div class="" style="min-height: 388px">
                                    <div
                                        class="w-full h-full relative"
                                        v-if="activeHistoryName == 'a'"
                                    >
                                        <div
                                            class="flex justify-end items-center mb-3 absolute right-0 -top-11"
                                        >
                                            <div
                                                v-if="
                                                    changeViewRunningType ==
                                                    'table'
                                                "
                                                class="mr-3"
                                            >
                                                <el-select-v2
                                                    style="width: 128px"
                                                    v-model="
                                                        selectedChargeStatus
                                                    "
                                                    :placeholder="
                                                        $t(
                                                            'alarm_options_quanbuzhuangtai'
                                                        )
                                                    "
                                                    @change="
                                                        handleChangeChargeStatus
                                                    "
                                                    :options="
                                                        chargeStatusOptions
                                                    "
                                                />
                                            </div>
                                            <div class="flex items-center">
                                                <date-search
                                                    v-if="
                                                        changeViewRunningType ==
                                                        'table'
                                                    "
                                                    :info="{
                                                        periodOptions: [],
                                                        datePickerType: 'day',
                                                        dayRangeLen: 9999,
                                                    }"
                                                    v-model:dateSelect="
                                                        runningDataRangeDate
                                                    "
                                                    @onChange="
                                                        onSearchRunningTableData
                                                    "
                                                />
                                                <el-date-picker
                                                    v-model="
                                                        runningDataRangeDate24
                                                    "
                                                    type="date"
                                                    :placeholder="
                                                        $t('Date Select')
                                                    "
                                                    value-format="YYYY-MM-DD"
                                                    style="width: 140px"
                                                    @change="
                                                        onSearchRunningTableData
                                                    "
                                                    :clearable="false"
                                                    v-if="
                                                        changeViewRunningType ==
                                                        'chart'
                                                    "
                                                />
                                            </div>

                                            <toggleView
                                                @change="onChangeViewRunning"
                                                v-model:type="
                                                    changeViewRunningType
                                                "
                                                class="ml-4"
                                            />
                                        </div>
                                        <div
                                            v-if="
                                                changeViewRunningType == 'table'
                                            "
                                            style="height: 333px"
                                            class="mt-3"
                                        >
                                            <div
                                                class="flex items-center justify-center"
                                            >
                                                <el-table
                                                    :data="runningTableData"
                                                    style="
                                                        width: 100%;
                                                        height: 333px;
                                                    "
                                                    class="tables h-full"
                                                >
                                                    <template
                                                        v-for="item in runningDataTableColumn"
                                                        :key="item.key"
                                                    >
                                                        <el-table-column
                                                            v-if="
                                                                item.key ==
                                                                'chargeAndDischargeQuantity'
                                                            "
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            align="center"
                                                            :width="240"
                                                        >
                                                            <template
                                                                #default="{
                                                                    row,
                                                                }"
                                                            >
                                                                {{
                                                                    row.chargeStatus ==
                                                                    '1'
                                                                        ? row.chargeQuantity
                                                                        : row.chargeStatus ==
                                                                          '2'
                                                                        ? row.dischargeQuantity
                                                                        : '-'
                                                                }}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column
                                                            v-else-if="
                                                                item.key ==
                                                                'chargeStatus'
                                                            "
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            :formatter="
                                                                formatterStatus
                                                            "
                                                            align="center"
                                                            :width="120"
                                                        />
                                                        <el-table-column
                                                            v-else-if="
                                                                item.key ==
                                                                'startSoc'
                                                            "
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            align="center"
                                                            :width="120"
                                                        >
                                                            <template
                                                                #default="{
                                                                    row,
                                                                }"
                                                            >
                                                                {{
                                                                    row.startSoc +
                                                                    '%'
                                                                }}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column
                                                            v-else-if="
                                                                item.key ==
                                                                'endSoc'
                                                            "
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            align="center"
                                                            :width="120"
                                                        >
                                                            <template
                                                                #default="{
                                                                    row,
                                                                }"
                                                            >
                                                                {{
                                                                    row.endSoc +
                                                                    '%'
                                                                }}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column
                                                            v-else-if="
                                                                item.key ==
                                                                'duration'
                                                            "
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            align="center"
                                                        >
                                                            <template
                                                                #default="{
                                                                    row,
                                                                }"
                                                            >
                                                                {{
                                                                    row.duration
                                                                }}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column
                                                            v-else-if="
                                                                item.key ==
                                                                'totalDuration'
                                                            "
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            align="center"
                                                            :width="
                                                                locale == 'en'
                                                                    ? '200'
                                                                    : '150'
                                                            "
                                                        >
                                                            <template
                                                                #default="{
                                                                    row,
                                                                }"
                                                            >
                                                                {{
                                                                    row.totalDuration
                                                                }}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column
                                                            v-else
                                                            :prop="item.key"
                                                            :label="item.title"
                                                            align="center"
                                                        />
                                                    </template>
                                                    <template #empty>
                                                        <empty-data
                                                            :description="
                                                                $t('zanwushuju')
                                                            "
                                                            style="
                                                                margin-top: 100px;
                                                            "
                                                        >
                                                            <slot
                                                                name="empty"
                                                            ></slot>
                                                        </empty-data>
                                                    </template>
                                                </el-table>
                                            </div>
                                            <div class="flex justify-end mt-4">
                                                <el-pagination
                                                    background
                                                    layout="prev, pager, next"
                                                    :total="
                                                        runningDataPageTotal
                                                    "
                                                    v-model:current-page="
                                                        runningDataPageInfo.current
                                                    "
                                                    :page-size="
                                                        runningDataPageInfo.size
                                                    "
                                                    @change="
                                                        runningDataPageChange
                                                    "
                                                    @current-change="
                                                        handleRunningDataCurrentChange
                                                    "
                                                />
                                            </div>
                                        </div>
                                        <div
                                            v-if="
                                                changeViewRunningType == 'chart'
                                            "
                                            class="mt-3"
                                        >
                                            <div class="text-center mb-1.5">
                                                <div
                                                    class="top-4 title-family flex justify-center items-center leading-6 text-title dark:text-title-dark"
                                                >
                                                    <span
                                                        class="mr-4 flex items-center"
                                                        ><span
                                                            class="raduis-box"
                                                        ></span
                                                        ><span class="ml-1">{{
                                                            $t('soc')
                                                        }}</span></span
                                                    >
                                                    <span
                                                        class="mr-4 flex items-center"
                                                        ><img
                                                            src="@/assets/device/lightning-3.png"
                                                        /><span class="ml-1">{{
                                                            $t(
                                                                'status_chongdian'
                                                            )
                                                        }}</span></span
                                                    >
                                                    <span
                                                        class="flex items-center"
                                                        ><img
                                                            src="@/assets/device/lightning-4.png"
                                                        /><span class="ml-1">{{
                                                            $t(
                                                                'status_fangdian'
                                                            )
                                                        }}</span></span
                                                    >
                                                </div>
                                            </div>
                                            <div class="demo-echart">
                                                <div
                                                    id="demo"
                                                    style="
                                                        height: 358px;
                                                        width: 100%;
                                                    "
                                                ></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="w-full h-full relative"
                                        v-if="activeHistoryName == 'b'"
                                    >
                                        <div
                                            class="w-full flex justify-end items-center gap-x-3 absolute right-0 -top-11"
                                        >
                                            <!-- <div
                                                class="flex items-center"
                                                v-if="chargeViewType == 'table'"
                                            >
                                                <el-select
                                                    v-model="deviceType"
                                                    :placeholder="
                                                        $t('Device Type')
                                                    "
                                                    style="width: 120px"
                                                    class=""
                                                    @change="onTypeChange"
                                                >
                                                    <el-option
                                                        v-for="item in deviceTypes"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value"
                                                    />
                                                </el-select>
                                            </div> -->

                                            <div class="flex items-center">
                                                <el-select
                                                    v-if="
                                                        chargeViewType ==
                                                        'table'
                                                    "
                                                    v-model="selectedFields"
                                                    :placeholder="
                                                        $t(
                                                            'station_zhanshiziduan'
                                                        )
                                                    "
                                                    style="width: 240px"
                                                    class=""
                                                    multiple
                                                    :multiple-limit="9999"
                                                    collapse-tags
                                                    collapse-tags-tooltip
                                                    filterable
                                                    @change="onShowFieldChange"
                                                >
                                                    <el-option
                                                        v-for="item in showFields"
                                                        :key="item.value"
                                                        :label="item.title"
                                                        :value="item.value"
                                                        :disabled="
                                                            item.value == 'time'
                                                        "
                                                    />
                                                </el-select>
                                                <el-select
                                                    v-if="
                                                        chargeViewType ==
                                                        'chart'
                                                    "
                                                    v-model="
                                                        selectDeviceLineDataFiled
                                                    "
                                                    :placeholder="
                                                        $t(
                                                            'station_zhanshiziduan'
                                                        )
                                                    "
                                                    style="width: 180px"
                                                    class=""
                                                    multiple
                                                    :multiple-limit="5"
                                                    collapse-tags
                                                    collapse-tags-tooltip
                                                    @change="
                                                        onShowDeviceLineChange
                                                    "
                                                >
                                                    <el-option
                                                        v-for="item in deviceLineDataFiled"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value"
                                                    />
                                                </el-select>
                                            </div>

                                            <div class="flex items-center">
                                                <date-search
                                                    v-if="
                                                        chargeViewType ==
                                                        'chart'
                                                    "
                                                    :info="{
                                                        periodOptions: [],
                                                        datePickerType: 'day',
                                                        minDate:
                                                            cascaderStartDate,
                                                        dayRangeLen: 2,
                                                    }"
                                                    v-model:dateSelect="
                                                        deviceRangeDate
                                                    "
                                                    @onChange="
                                                        onChartsDateChange
                                                    "
                                                />
                                                <date-search
                                                    v-if="
                                                        chargeViewType ==
                                                        'table'
                                                    "
                                                    isTimerPicker
                                                    :info="{
                                                        periodOptions: [],
                                                        datePickerType: 'day',
                                                        minDate:
                                                            cascaderStartDate,
                                                        dayRangeLen: 2,
                                                    }"
                                                    v-model:dateSelect="
                                                        rangeDate
                                                    "
                                                    @onChange="onDateChange"
                                                />
                                            </div>
                                            <toggleView
                                                @change="onChangeView"
                                                v-model:type="chargeViewType"
                                            />
                                        </div>
                                        <!--  style="height: 400px" -->
                                        <div
                                            class="table-tabs mt-3"
                                            v-if="chargeViewType == 'table'"
                                        >
                                            <device-data
                                                :tableData="otherTableData"
                                                :tableColumn="otherTableColumn"
                                                :loading="tableLoading"
                                                :showMore="!!showMore"
                                                @loadMore="loadMore"
                                                :loadMoreLoading="
                                                    loadMoreLoading
                                                "
                                            />
                                            <div class="text-center mt-4">
                                                <el-button
                                                    plain
                                                    round
                                                    v-if="!!showMore"
                                                    :loading="loadMoreLoading"
                                                    @click="loadMore"
                                                    >{{
                                                        $t('jiazaigengduo')
                                                    }}</el-button
                                                >
                                                <div
                                                    v-if="
                                                        !showMore &&
                                                        otherTableData.length
                                                    "
                                                    class="text-secondar-text dark:text-60-dark"
                                                >
                                                    {{ $t('No more data') }}
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="table-tabs mt-3 deviceLineLoading"
                                            v-if="chargeViewType == 'chart'"
                                        >
                                            <div style="height: 388px">
                                                <!-- <a-spin
                                                        :spinning="spinning"
                                                    > -->
                                                <div
                                                    class="e-height-s"
                                                    id="deviceLine"
                                                    style="
                                                        width: 100%;
                                                        height: 388px;
                                                    "
                                                ></div>
                                                <!-- <div
                                                        class=""
                                                        v-if="
                                                            !deviceLineData?.length
                                                        "
                                                    >
                                                        <empty-data
                                                            :description="
                                                                $t('zanwushuju')
                                                            "
                                                            style="
                                                                margin-top: 100px;
                                                            "
                                                        >
                                                            <slot
                                                                name="empty"
                                                            ></slot>
                                                        </empty-data>
                                                    </div> -->
                                                <!-- </a-spin> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="b">
                        <div class="p-4 bg-ff dark:bg-ff-dark rounded-lg">
                            <div
                                class="flex justify-between items-center mb-6 text-title dark:text-title-dark"
                            >
                                <el-tabs
                                    v-model="statisticsActiveName"
                                    @tab-change="
                                        handleStatisticsActiveNameTabChange
                                    "
                                >
                                    <el-tab-pane
                                        :label="$t('Average running time')"
                                        name="b"
                                    >
                                    </el-tab-pane>
                                    <el-tab-pane
                                        :label="
                                            $t('Charge and discharge capacity')
                                        "
                                        name="a"
                                    >
                                    </el-tab-pane>
                                </el-tabs>
                                <div class="flex justify-between gap-x-3">
                                    <el-select
                                        v-model="projectChargeTotalDateType"
                                        placeholder="请选择"
                                        style="
                                            width: 120px;
                                            margin-right: 16px;
                                            border-radius: 8px;
                                        "
                                        @change="onProjectChargeTotalDateChange"
                                    >
                                        <el-option
                                            v-for="item in chargeTotalDateOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </div>
                            </div>
                            <!-- 393px -->
                            <div class="flex gap-x-5">
                                <div class="flex-1 w-0">
                                    <div
                                        class="w-full"
                                        style="height: 306px"
                                        id="incomeEcharts"
                                    ></div>
                                </div>
                                <rank
                                    :rankList="rankList"
                                    @toggleRank="toggleRank"
                                    :type="statisticsActiveName"
                                />
                            </div>
                        </div>
                        <div
                            class="flex items-center flex-row-reverse gap-x-3 mt-3"
                        >
                            <div
                                class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4"
                            >
                                <div
                                    class="w-full flex justify-between items-center text-title dark:text-title-dark"
                                >
                                    <div>
                                        {{ $t('Operating Hours Distribution') }}
                                    </div>
                                    <div></div>
                                </div>
                                <div class="w-full" style="height: 336px">
                                    <distributionChart
                                        :chartData="distributionChartDataDur"
                                        type="hour"
                                    />
                                </div>
                            </div>
                            <div
                                class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4"
                            >
                                <div
                                    class="w-full flex justify-between items-center text-title dark:text-title-dark"
                                >
                                    <div>
                                        {{ $t('Service Life Distribution') }}
                                    </div>
                                    <div></div>
                                </div>
                                <div class="w-full" style="height: 336px">
                                    <distributionChart
                                        :chartData="distributionChartDataYears"
                                        type="month"
                                    />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="3" tab="c">
                        <div
                            class="p-4 bg-ff dark:bg-ff-dark rounded-lg"
                            style="min-height: 360px"
                        >
                            <alarm-overview
                                station-type="vehicle_battery"
                                v-model:getAlarmDataFlag="getAlarmDataFlag"
                            />
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <edit-device-name
                v-model:visible="editNameVisible"
                @update="onUpdateDevice"
                :supplierId="customerDetail.customerId"
            />
            <!-- <edit-device
            v-model:visible="editDeviceVisible"
            :device-list="deviceList"
            @update="onUpdateDevice"
        /> -->
        </div>
    </a-spin>
</template>

<script>
import * as echarts from 'echarts'
import {
    getChargeOption,
    getElectricityData,
    unitConversion,
    alternateUnits,
    updateEcharts,
    filterDate,
    getTimeOption,
    someMax,
    roundNumFun,
    realTimeDistributedOptions,
    getPowerLineData,
    analysisdOptions,
    formatterDeviceName,
} from '../const'

import { getState, getSegmentTypeColor } from '@/common/util.js'
import {
    ref,
    onMounted,
    reactive,
    nextTick,
    computed,
    onBeforeUnmount,
    watch,
    unref,
} from 'vue'

import { useRoute, useRouter } from 'vue-router'
import carApi from '@/apiService/car'
import powerApi from '@/apiService/power'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import _round from 'lodash/round'
import basicInfo from './components/basicInfo.vue'
import realTimeInfo from './components/realTimeInfo.vue'
import mainInfo from './components/mainInfo.vue'
// import cellBox from './components/cellBox.vue'

import Percentage from '../components/percentage.vue'
import { useStore } from 'vuex'
import AlarmOverview from './components/alarmOverview.vue'
import moment from 'moment'
import * as XLSX from 'xlsx' //引入 xlsx 库，将数据转换为 Excel 并下载
import DateSearch from '@/components/dateSearch.vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import carImg from './components/carImg.vue'
import statusPieChart from './components/statusPieChartPower.vue'
import socPieChart from './components/socPieChart.vue'
// import BarProgress from '../components/barProgress.vue'
import distributionChart from './components/distributionChart.vue'
import EditDeviceName from './components/editDeviceName.vue'
import { message } from 'ant-design-vue'
import { ElLoading, ClickOutside } from 'element-plus'
import toggleView from '@/components/toggle.vue'
// import ChargeData from '@/views/role/components/carChargeData.vue'
import deviceData from '@/views/role/components/deviceDataE.vue'
import { useI18n } from 'vue-i18n'
import useTheme from '@/common/useTheme'
import Decimal from 'decimal.js'
import debounce from 'lodash/debounce'
import rank from './components/rank.vue'
import { convertTableHeaders, roundUp } from './util/util.js'
export default {
    name: 'deviceDetail',
    //
    directives: {
        'click-outside': ClickOutside,
    },
    components: {
        // cellBox,
        basicInfo,
        realTimeInfo,
        mainInfo,
        Percentage,
        // WorkOrder,
        AlarmOverview,
        DateSearch,
        carImg,
        statusPieChart,
        socPieChart,
        // BarProgress,
        distributionChart,
        toggleView,
        // ChargeData,
        deviceData,
        EditDeviceName,
        rank,
    },
    setup() {
        const { t, locale } = useI18n()
        const loading = ref(false)

        const store = useStore()

        const route = useRoute()
        const customerDetail = ref(route.query)
        const router = useRouter()

        //1表示展示整个机柜数据
        const defaultImg = require('@/assets/car/rv1.png')
        //获取头部站点信息
        const stationInfo = reactive({
            alarmQuantity: void 0,
            avgTemperature: void 0,
            createTime: void 0,
            address: void 0,
            installedCapacity: void 0,
            installedPower: void 0,
            soc: void 0,
            soh: void 0,
            stationNo: void 0,
            status: void 0,
            stationName: undefined,
            stationPic: defaultImg,
            id: undefined,
        })

        //获取三个块数据
        const chargeStatisticsData = reactive({
            beforeYesterdayChargeDur: 0,
            beforeYesterdayDischargeDur: 0,
            comparedChargePercent: 0,
            comparedDischargePercent: 0,
            currentMonthChargeDur: 0,
            currentMonthDischargeDur: 0,
            totalChargeDur: 0,
            totalDischargeDur: 0,
            yesterdayChargeDur: 0,
            yesterdayDischargeDur: 0,
        })
        // 统计站点tab2 充放电信息统计
        //
        const setChargeOptions = () => {
            let options = _cloneDeep(getChargeOption())
            options.grid.bottom = 0
            if (dateList2.value?.length) {
                options.xAxis.data = dateList2.value
            } else {
                if (projectChargeTotalDateType.value === '1') {
                    options.xAxis.data = Array.from({ length: 7 }, (_, i) => {
                        return dayjs()
                            .subtract(6 - i, 'day')
                            .format('MM/DD')
                    })
                } else if (projectChargeTotalDateType.value === '2') {
                    options.xAxis.data = Array.from({ length: 12 }, (_, i) => {
                        return dayjs()
                            .subtract(11 - i, 'month')
                            .format('YYYY-MM')
                    })
                }
            }

            if (projectChargeTotalDateType.value === '1') {
                options.series[0].barWidth = '24px'
                options.series[1].barWidth = '24px'
            } else {
                //
                options.series[0].barWidth = '18px'
                options.series[1].barWidth = '18px'
            }

            if (statisticsActiveName.value == 'a') {
                options.yAxis.name = 'Ah'
                options.series[0].data = chargeData2.value
                options.series[1].data = dischargeData2.value
            } else if (statisticsActiveName.value == 'b') {
                options.yAxis.name = 'h'
                options.series[0].data = chargeDur2.value
                options.series[1].data = disChargeDur2.value
                options.series[0].name = t('Charging duration')
                options.series[1].name = t('Discharging duration')
                options.legend.data = [
                    t('Charging duration'),
                    t('Discharging duration'),
                ]
            }
            updateEcharts('incomeEcharts', options)
        }
        const dateList2 = ref()
        const chargeData2 = ref([])
        const dischargeData2 = ref([])
        const chargeDur2 = ref([])
        const disChargeDur2 = ref([])
        // 获取总统计
        const getCharge = async (params, id, option) => {
            //
            try {
                const {
                    data: { data, code },
                } = await powerApi.statsPowerBattDailyUsage(params)
                if (code === 0) {
                    dateList2.value = data.map((item) => {
                        if (params.periodType == 'month') {
                            return dayjs(item.date).format('YYYY-MM')
                        }
                        return dayjs(item.date).format('MM/DD')
                    })
                    chargeData2.value = data.map((item) => {
                        return item.chargeCap
                    })
                    dischargeData2.value = data.map((item) => {
                        return item.dischargeCap
                    })
                    chargeDur2.value = data.map((item) => {
                        return item.chargeDur
                    })
                    disChargeDur2.value = data.map((item) => {
                        return item.dischargeDur
                    })
                    setChargeOptions()
                } else {
                    updateEcharts(id, option)
                }
            } catch (error) {
                updateEcharts(id, option)
            }
        }
        //获取车辆tab
        const initDom = () => {
            const options = _cloneDeep(getTimeOption())
            options.grid.left = '40px'
            options.grid.right = '40px'
            options.xAxis[0].data = time24H.value
            options.xAxis[1].data = time24H.value
            options.series[0].data = echartsData24H.value
            options.series[1].data = s224H.value
            const demo = document.getElementById('demo')
            if (demo) {
                demo && echarts.dispose(demo)
                const setOption = echarts.init(demo)
                nextTick(() => {
                    if (setOption) {
                        setOption.setOption(options)
                    }
                })
            }
        }
        // 切换24小时电量日期选择框
        const echartsData24H = ref([])

        const { themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                if (activeKey.value === '1') {
                    if (activeName.value === 'a') {
                        if (totalAnalysisName.value === 'a') {
                            handleDeviceStatusTabChange()
                        } else if (totalAnalysisName.value === 'b') {
                            if (analysisActiveName.value == 'a') {
                                nextTick(() => {
                                    setChargeTimeDistributionChart()
                                })
                            } else if (analysisActiveName.value == 'b') {
                                nextTick(() => {
                                    setChargeStartSocDistributionChart()
                                })
                            } else if (analysisActiveName.value == 'c') {
                                nextTick(() => {
                                    setChargeCapacityDistributionChart()
                                })
                            } else {
                                setChargeTimeDistributionChart()
                                setChargeStartSocDistributionChart()
                                setChargeCapacityDistributionChart()
                            }
                        }
                    } else if (activeName.value === 'b') {
                        if (activeDistributionName.value === 'a') {
                            updateDistributionChart()
                            updateAnalysisChart()
                        } else if (activeDistributionName.value === 'b') {
                            updateDistributionChart()
                            updateAnalysisChart('temp')
                        }
                    } else if (activeName.value === 'c') {
                        if (
                            activeHistoryName.value === 'a' &&
                            changeViewRunningType.value === 'chart'
                        ) {
                            initDom()
                        } else if (
                            activeHistoryName.value === 'b' &&
                            chargeViewType.value === 'chart'
                        ) {
                            setDeviceLineChart()
                        }
                    }
                } else if (activeKey.value === '2') {
                    setChargeOptions()
                } else if (activeKey.value === '3') {
                    // No action needed for tab 3
                }
            }
        })
        // 获取收告警统计
        const alarmData = ref({
            totalQuantity: undefined,
            processingQuantity: undefined,
            todayQuantity: undefined,
            sevenDayQuantity: undefined,
        })
        const getStatisticalCard = async () => {
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let res = await powerApi.getStatisticalCard({
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            })
            alarmData.value = res.data.data
        }
        const projectData = ref()
        const getMainDetailInfo = async () => {
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            if (customerDetail.value.customerId) {
                // 客户视角：调用客户详情接口
                let res = await powerApi.getCustomerBmsSummary({
                    customerId: customerDetail.value.customerId,
                })
                const customerData = res.data.data

                // 将客户视角的数据结构转换为与项目视角一致的格式
                projectData.value = {
                    projectName: customerData.customerName, // 客户名称作为项目名称
                    projectNo: customerData.customerName, // 客户名称作为项目编号
                    createDate: customerData.commissioningDate, // 投运日期
                    ratedPower: customerData.totalCapacity, // 额定功率
                    majorCustomers: customerData.customerName, // 主要客户
                    stationPic: defaultImg,
                    // 构建 bmsSummaryInfo 结构
                    bmsSummaryInfo: {
                        totalDevices: customerData.totalDevices,
                        activeCount: customerData.activeCount,
                        onlineCount: customerData.onlineCount,
                        dsgTimeSum: customerData.dsgTimeSum,
                        chgTimeSum: customerData.chgTimeSum,
                        totalEnergy: customerData.totalEnergy,
                    },
                    // 其他可能需要的字段
                    totalCapacity: customerData.totalCapacity,
                    customerName: customerData.customerName,
                    supplierName: customerData.supplierName,
                }
            } else if (customerDetail.value.projectId) {
                // 项目视角：调用项目详情接口
                let res = await powerApi.projectDetail({
                    id: customerDetail.value.projectId,
                })
                projectData.value = res.data.data
                Object.keys(res.data.data).forEach((key) => {
                    projectData.value[key] = res.data.data[key]
                })
                projectData.value['stationPic'] = defaultImg
                projectData.value.majorCustomers =
                    res.data.data.majorCustomers.join('、')
            }
        }
        const statusData = ref({
            0: 0,
            1: 0,
            2: 0,
            3: 0,
        })

        const statsDevicesStatusCount = async () => {
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            try {
                let res = await powerApi.statsDevicesStatusCount({
                    projectId: customerDetail.value.projectId,
                    customerId: customerDetail.value.customerId,
                })
                Object.assign(statusData.value, res.data.data)
            } catch (error) {
                //
                return
            }
        }
        const SOCData = ref({
            1: 0,
            2: 0,
            3: 0,
        })
        const statsDeviceSocProportion = async () => {
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let res = await powerApi.statsDeviceSocProportion({
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            })
            Object.assign(SOCData.value, res.data.data)
        }
        const activeData = reactive({})
        const statsActiveDeviceCount = async () => {
            //
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let res = await powerApi.statsActiveDeviceCount({
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            })

            activeData.beforeYesterdayActiveCount =
                res.data.data.beforeYesterdayActiveCount
            activeData.comparedBeforeYesterdayPercent =
                res.data.data.comparedBeforeYesterdayPercent
            activeData.yesterdayActiveCount = res.data.data.yesterdayActiveCount
        }

        const statsPowerBattUsageSummary = async () => {
            //
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let res = await powerApi.statsPowerBattDurUsageSummary({
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            })
            if (res.data.code === 0) {
                Object.keys(res.data.data).forEach((key) => {
                    chargeStatisticsData[key] = res.data.data[key] || 0
                })
            } else {
                Object.keys(chargeStatisticsData).forEach((key) => {
                    chargeStatisticsData[key] = 0
                })
            }
        }

        const getMainInfo = async () => {
            //
            // if (
            //     !customerDetail.value.projectId &&
            //     !customerDetail.value.customerId
            // ) {
            //     return
            // }
            // 获取活跃车辆统计
            // if (
            //     customerDetail.value.projectId ||
            //     customerDetail.value.customerId
            // ) {

            // }
            await getMainDetailInfo()
            await statsDevicesStatusCount()
            await statsDeviceSocProportion()
            await statsActiveDeviceCount()
            await statsPowerBattUsageSummary() // 统计tabs2站点充放电信息统计
            await getDevicePage()
            await getBasicInfo()
            await getRealInfo() // 实时基础信息
            await getBmsDataColumn() // 获取表头
            await getstatsPowerBattDailyUsage() // 设备状态统计，充放电量
            await getStatisticalCard() // 获取tabs3异常统计
            //

            //
        }
        const deviceSearchKeyword = ref('')
        const equipmentData = ref([])
        const getDevicePage = async (tag) => {
            //
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let params = {
                size: 1000,
                current: 1,
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
                keyword: deviceSearchKeyword.value,
            }
            let res = await powerApi.getDevicePageList(params)
            if (route.query.sn) {
                //
            } else {
                if (!tag) {
                    currentDeviceSn.value = res.data.data.records[0].sn
                }
            }
            equipmentData.value = res.data.data.records
        }
        const onSearchDevice = debounce(async () => {
            await getDevicePage(true)
        }, 300)
        const editNameVisible = ref(false)
        const onEditName = () => {
            //
            editNameVisible.value = true
        }
        const onUpdateDevice = () => {
            getMainInfo()
        }
        onMounted(async () => {
            localStorage.setItem('activeSystem', 'car')
            loading.value = true
            // 获取站点基本信息静态信息
            currentDeviceSn.value = route.query.sn

            await getMainInfo()

            await store.dispatch('dictionary/getDictionary', 'vehicleType')
            loading.value = false
        })

        // 页面销毁前
        onBeforeUnmount(() => {
            const demo = document.getElementById('demo')
            demo && echarts.dispose(demo)
            const chartDischargeDom = document.getElementById('chartDischarge')
            chartDischargeDom && echarts.dispose(chartDischargeDom)
        })

        const goRouter = () => {
            if (window.history.state?.back) {
                router.go(-1)
            } else {
                router.replace('/vehicle')
            }
        }

        //
        const activeKey = ref('1')
        // 充放电日期选择
        const chargeDateSearchChange = async () => {
            //
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let params
            if (projectChargeTotalDateType.value == '1') {
                params = {
                    periodType: 'day',
                    startDate: moment()
                        .subtract(6, 'days')
                        .format('YYYY-MM-DD'),
                    endDate: moment().format('YYYY-MM-DD'),
                    projectId: customerDetail.value.projectId,
                    customerId: customerDetail.value.customerId,
                }
            } else if (projectChargeTotalDateType.value == '2') {
                params = {
                    periodType: 'month',
                    startMonth: moment()
                        .subtract(11, 'months')
                        .format('YYYY-MM'),
                    endMonth: moment().format('YYYY-MM'),
                    projectId: customerDetail.value.projectId,
                    customerId: customerDetail.value.customerId,
                }
            }
            await getCharge(params, 'incomeEcharts', getChargeOption())
            getRankData() // 获取排名
        }
        //充放电统计
        const projectChargeTotalDateType = ref('1')
        const onProjectChargeTotalDateChange = async () => {
            //
            chargeDateSearchChange()
        }
        // 运行时长

        const statisticsActiveName = ref('b')
        const handleStatisticsActiveNameTabChange = async () => {
            //
            setChargeOptions()
            getRankData()
        }
        const tabsList = ref(['1'])
        // const
        const distributionChartDataYears = ref()
        const distributionChartDataDur = ref()

        const transformData = (arr, xKey, yKey) => {
            if (!arr || arr.length === 0) {
                return []
            }
            const maxX = roundUp(Math.max(...arr.map((item) => item.time)))
            const timeMap = arr.reduce((acc, curr) => {
                acc[curr[xKey]] = curr[yKey]
                return acc
            }, {})

            // 生成结果数组
            const result = Array.from({ length: maxX + 1 }, (_, index) => ({
                x: index, // x只是指x轴数据，可以为别的，但是在组件内部已经定义了x
                value: timeMap[index] || 0, // 存在则取值，不存在则默认为0
            }))
            return result
        }
        const getStatisticDeviceData = async () => {
            //
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let res = await powerApi.statisticDeviceUseTimeDistribution({
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            })
            let result = transformData(res.data.data, 'time', 'quantity')
            distributionChartDataYears.value = result
            let res1 = await powerApi.statisticDeviceRunTimeDistribution({
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            })
            let result1 = transformData(res1.data.data, 'time', 'quantity')
            distributionChartDataDur.value = result1
        }
        const changeTab = (key) => {
            activeKey.value = key
            if (tabsList.value.includes(key)) {
                return
            } else {
                tabsList.value.push(key)
                if (key === '2') {
                    nextTick(() => {
                        // 统计和排名统计都在这个方法里
                        if (
                            customerDetail.value.projectId ||
                            customerDetail.value.customerId
                        ) {
                            chargeDateSearchChange()
                            getStatisticDeviceData() // 下面统计
                        }
                    })
                } else if (key === '3') {
                    getAlarmDataFlag.value = false
                    setTimeout(() => {
                        getAlarmDataFlag.value = true
                    }, 100)
                }
            }
        }
        // 异常
        const getAlarmDataFlag = ref(false)

        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        // 开启

        // 车辆新属性。多余旧数据后续删除
        const activeDeviceInfo = computed(() => {
            return equipmentData.value.find(
                (item) => item.sn === currentDeviceSn.value
            )
        })
        const cellData = ref([])
        const showBmsBox = ref(false)

        const rankList = ref([])
        const profitUnit = ref('Ah')
        const getRankData = async () => {
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let params = {
                periodType: 'day',
                startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
                endDate: moment().format('YYYY-MM-DD'),
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
                chargeType: chargeType.value,
                rankType:
                    statisticsActiveName.value == 'a' ? 'capacity' : 'duration',
            }
            if (projectChargeTotalDateType.value === '1') {
                params.startDate = moment()
                    .subtract(6, 'days')
                    .format('YYYY-MM-DD')
            } else if (projectChargeTotalDateType.value === '2') {
                params.startDate = moment()
                    .subtract(365, 'days')
                    .format('YYYY-MM-DD')
            }
            const res = await powerApi.statsDeviceChargeRank(params)
            rankList.value = res.data.data

            let max = 0
            rankList.value.forEach((item) => {
                if (+max < +item.quantity) max = +item.quantity
            })
            // 处理数据，计算百分比
            if (max) {
                rankList.value.forEach((item) => {
                    item['ratio'] = +(item.quantity / max).toFixed(2)
                })
            }
            rankList.value.sort((a, b) => {
                return b.quantity - a.quantity
            })
        }
        const chargeType = ref('charge')
        const toggleRank = async (e) => {
            chargeType.value = e
            await getRankData()
        }
        const OffDevice = async () => {
            //
            try {
                let res = await powerApi.pushLockCommand({
                    sn: currentDeviceSn.value,
                    lockCommand:
                        realData.value.lockSta == '1' ? 'unlock' : 'lock', // lock锁定 。unlock解锁
                })
                if (res.data.data) {
                    message.success(
                        realData.value.lockSta == '1' ? '解锁成功' : '锁车成功'
                    )
                }
            } catch (error) {
                //
            }
        }
        const handleRefresh = async () => {
            //
            // 默认获取实时状态信息。 因为要根据其中的值设置设备数据的表头
            await getRealInfo() // 实时基础信息
            await getBmsDataColumn() // 获取表头

            if (activeName.value == 'a') {
                // 基础信息tab
                await getBasicInfo() // 基础信息
                if (totalAnalysisName.value == 'a') {
                    await getstatsPowerBattDailyUsage() // 设备状态统计tab
                } else {
                    await handleChargeAnalysisDateChange()
                }
            } else if (activeName.value == 'b') {
                //   实时状态tab
                await getRealData()
            } else if (activeName.value == 'c') {
                // 历史数据tab
                showMore.value = undefined
                otherTableData.value = []
                allOtherTableData.value = []

                if (activeHistoryName.value == 'b') {
                    if (chargeViewType.value == 'chart') {
                        await onChartsDateChange()
                    } else {
                        if (allOtherTableData.value.length) {
                            //
                        } else {
                            await onSearchOther(false)
                        }
                    }
                } else {
                    await getRunningData()
                }
            }
            message.success(t('Successed'))
        }
        const selectSupplierInfoCar = computed(() => {
            return store.state.car.selectSupplierInfoCar?.name
                ? store.state.car.selectSupplierInfoCar
                : localStorage.getItem('selectSupplierInfoCar') &&
                  localStorage.getItem('selectSupplierInfoCar') != 'undefined'
                ? JSON.parse(localStorage.getItem('selectSupplierInfoCar'))
                : undefined
        })
        const chargeViewType = ref('chart')
        const onChangeView = async (e) => {
            if (chargeViewType.value == 'chart') {
                onChartsDateChange()
            } else {
                if (allOtherTableData.value.length) {
                    //
                } else {
                    onSearchOther(false)
                }
            }
        }

        //
        // 数据3详情
        const pageInfo = ref({
            current: 1,
            size: 100,
        })
        const pageTotal = ref(0)

        const deviceType = ref('BMS')
        const types = ref([
            {
                label: 'BMS',
                value: 'BMS',
            },
            {
                label: t('Cell'),
                value: 'Cell',
            },
        ])
        const deviceTypes = computed(() => {
            return types.value
        })
        const selectedFields = ref([])
        const showFields = ref([])
        // 类型切换
        const tableLoading = ref(false)
        const onTypeChange = async () => {
            otherTableData.value = []
            allOtherTableData.value = []
            showMore.value = undefined
            tableLoading.value = true
            if (pageInfo.value.current == 1) {
                await onSearchOther(false)
            } else {
                pageInfo.value.current = 1
            }
            tableLoading.value = false
        }
        const otherTableData = ref([])
        const allOtherTableData = ref([])

        const onShowFieldChange = async (e) => {}
        // 日期选择  ⬇️
        const cascaderStartDate = computed(() => {
            return stationInfo.createTime || '1971-01-01'
        })
        // 这个可以根据站点详情返回的投运日期
        const rangeDate = ref({
            startDate: moment()
                .subtract(1, 'day')
                .format('YYYY-MM-DD HH:mm:ss'),
            endDate: moment().format('YYYY-MM-DD 23:59:59'),
        })
        const onDateChange = async () => {
            otherTableData.value = []
            allOtherTableData.value = []
            showMore.value = undefined
            tableLoading.value = true
            if (pageInfo.value.current == 1) {
                await onSearchOther(true)
            } else {
                pageInfo.value.current = 1
            }
            tableLoading.value = false
        }
        const deviceRangeDate = ref({
            periodType: 'day',
            startDate: moment().subtract(2, 'days').format('YYYY-MM-DD'),
            endDate: moment().subtract(0, 'days').format('YYYY-MM-DD'),
        })
        const deviceLineData = ref()
        const selectDeviceLineDataFiled = ref([
            'soc',
            'soh',
            'sysVoltage',
            'sysCurrent',
        ])
        const onShowDeviceLineChange = () => {
            setDeviceLineChart()
        }
        const deviceLineDataFiled = ref([
            {
                value: 'soc',
                label: t('SOC') + '(%)',
            },
            {
                value: 'soh',
                label: t('SOH') + '(%)',
            },
            {
                value: 'sysVoltage',
                label: t('sys_voltage') + '(V)',
            },
            {
                value: 'sysCurrent',
                label: t('sys_currents') + '(A)',
            },
            {
                value: 'insZ',
                label: t('ins_p') + '(kΩ)',
            },
            {
                value: 'insF',
                label: t('ins_n') + '(kΩ)',
            },
            {
                value: 'volMax',
                label: t('vol_max') + '(V)',
            },
            {
                value: 'volMin',
                label: t('vol_min') + '(V)',
            },
            {
                value: 'volDif',
                label: t('vol_dif') + '(V)',
            },
            {
                value: 'tempMax',
                label: t('temp_max') + '(℃)',
            },
            {
                value: 'tempMin',
                label: t('temp_min') + '(℃)',
            },
            {
                value: 'chgerOutVolt',
                label: t('chger_out_volt') + '(V)',
            },
            {
                value: 'chgerOutCurr',
                label: t('chger_out_curr') + '(A)',
            },
            {
                value: 'ccRes',
                label: t('cc_res'),
            },
            {
                value: 'cc2Res',
                label: t('cc2_res'),
            },
            {
                value: 'cpFreq',
                label: t('cp_freq'),
            },
            {
                value: 'cpPwm',
                label: t('cp_pwm'),
            },
            {
                value: 'dsgCapSum',
                label: t('Total Discharge Cap') + '(Ah)',
            },
            {
                value: 'chgCapSum',
                label: t('Total Charge Cap') + '(Ah)',
            },
        ])
        const setDeviceLineChart = () => {
            //
            const options = _cloneDeep(getPowerLineData())
            // deviceLineData.value
            options.series = []
            if (
                selectDeviceLineDataFiled.value &&
                selectDeviceLineDataFiled.value.length > 0
            ) {
                selectDeviceLineDataFiled.value.forEach((field) => {
                    const fieldConfig = deviceLineDataFiled.value.find(
                        (item) => item.value === field
                    )
                    if (fieldConfig) {
                        options.series.push({
                            name: fieldConfig.label,
                            type: 'line',
                            data: deviceLineData.value.map(
                                (item) => item[field]
                            ),
                            smooth: true,
                            showSymbol: false,
                        })
                    }
                })
            }
            options.yAxis[0].name = ''
            options.yAxis[0].show = false
            options.yAxis[1].show = false
            options.grid.left = '20px'
            options.grid.right = '10px'
            // options.yAxis[1] = undefined
            options.xAxis[0].data = deviceLineData.value.map((item) => {
                return moment(new Date(item.time * 1000)).format('HH:mm:ss')
            })
            options.dataZoom[0].height = 15
            options.dataZoom[0].bottom = 10
            options.grid['bottom'] = '50px'
            updateEcharts('deviceLine', options)
        }
        const onChartsDateChange = async () => {
            const loadingEchart = ElLoading.service({
                target: 'deviceLineLoading',
                lock: true,
                text: '',
                background: 'rgba(255, 255, 255, 0.6)',
            })

            let params = {
                sn: currentDeviceSn.value,
                dataType: 'BMS',
                startDate: deviceRangeDate.value.startDate,
                endDate: deviceRangeDate.value.endDate,
            }
            const res = await powerApi.getBmsDataLog(params)
            if (res.data.data) {
                deviceLineData.value = res.data.data.records.map((item) => {
                    return {
                        soc: item.soc,
                        soh: item.soh,
                        sysVoltage: item.sysVoltage,
                        sysCurrent: item.sysCurrent,
                        insZ: item.insZ,
                        insF: item.insF,
                        volMax: item.volMax,
                        volMin: item.volMin,
                        volDif: item.volDif,
                        tempMax: item.tempMax,
                        tempMin: item.tempMin,
                        chgerOutVolt: item.chgerOutVolt,
                        chgerOutCurr: item.chgerOutCurr,
                        ccRes: item.ccRes,
                        cc2Res: item.cc2Res,
                        cpFreq: item.cpFreq,
                        cpPwm: item.cpPwm,
                        dsgCapSum: item.dsgCapSum,
                        chgCapSum: item.chgCapSum,
                        time: item.time,
                    }
                })
                setDeviceLineChart()
            } else {
                // pageInfo.value.current = current - 1
            }
            loadingEchart.close()
        }
        const showMore = ref(undefined)
        const getBmsDataTable = async (flag) => {
            let params = {
                stationNo: route.query.stationNo,
                sn: currentDeviceSn.value,
                dataType: deviceType.value,
                startTime: rangeDate.value.startDate,
                endTime: rangeDate.value.endDate,
                ...pageInfo.value,
                nextToken: showMore.value,
            }
            const res = await powerApi.getBmsDataPage(params)
            if (res.data.data) {
                pageTotal.value = res.data.data.total // 总数  暂时无效
                allOtherTableData.value = allOtherTableData.value.concat(
                    res.data.data.records
                )
                showMore.value = res.data.data.nextToken || undefined
            } else {
                // pageInfo.value.current = current - 1
            }
        }
        const getBmsDataColumn = async () => {
            //
            let res = await powerApi.getBmsDataColumn({
                productType: 'power_battery',
                deviceType: deviceType.value,
                bmsSn: currentDeviceSn.value,
            })
            showFields.value = convertTableHeaders(
                res.data.data,
                t,
                locale.value
            )
            // showFields.value = [
            //     {
            //         value: 'time',
            //         title: t('Collection time'),
            //         key: 'time',
            //         dataKey: 'time',
            //         width: 200,
            //         cellRenderer: ({ cellData: time }) =>
            //             moment(new Date(time * 1000)).format(
            //                 'YYYY-MM-DD HH:mm:ss'
            //             ),
            //     },
            //     {
            //         value: 'sn',
            //         title: t('Device No'),
            //         key: 'sn',
            //         dataKey: 'sn',
            //         width: 150,
            //     },
            //     {
            //         value: 'cellNub',
            //         title: t('Cell Series Count'),
            //         key: 'cellNub',
            //         dataKey: 'cellNub',
            //         width: locale.value == 'en' ? 136 : 120,
            //     },
            //     {
            //         value: 'tempNub',
            //         title: t('Temperature sensing number'),
            //         key: 'tempNub',
            //         dataKey: 'tempNub',
            //         width: locale.value == 'en' ? 212 : 120,
            //     },
            //     {
            //         value: 'sysCapacity',
            //         title: t('System Capacity') + '(Ah)',
            //         key: 'sysCapacity',
            //         dataKey: 'sysCapacity',
            //         width: locale.value == 'en' ? 156 : 120,
            //     },
            //     {
            //         value: 'sysVoltage',
            //         title: t('sys_voltage') + '(V)',
            //         key: 'sysVoltage',
            //         dataKey: 'sysVoltage',
            //         width: locale.value == 'en' ? 156 : 120,
            //     },
            //     {
            //         value: 'sysCurrent',
            //         title: t('sys_currents') + '(A)',
            //         key: 'sysCurrent',
            //         dataKey: 'sysCurrent',
            //         width: locale.value == 'en' ? 156 : 120,
            //     },
            //     {
            //         value: 'soc',
            //         title: t('System SOC') + '(%)',
            //         key: 'soc',
            //         dataKey: 'soc',
            //         width: locale.value == 'en' ? 128 : 120,
            //     },
            //     {
            //         value: 'soh',
            //         title: t('System SOH') + '(%)',
            //         key: 'soh',
            //         dataKey: 'soh',
            //         width: locale.value == 'en' ? 128 : 120,
            //     },
            //     {
            //         value: 'status',
            //         title: t('Charging and discharging status'),
            //         key: 'status',
            //         dataKey: 'status',
            //         width: locale.value == 'en' ? 240 : 150,
            //         cellRenderer: ({ cellData: status }) => {
            //             return formatterStatus({ chargeStatus: status })
            //         },
            //     },
            //     {
            //         value: 'chgCapSum',
            //         title: t('Total Charge Cap') + '(Ah)',
            //         key: 'chgCapSum',
            //         dataKey: 'chgCapSum',
            //         width: locale.value == 'en' ? 168 : 120,
            //     },
            //     {
            //         value: 'dsgCapSum',
            //         title: t('Total Discharge Cap') + '(Ah)',
            //         key: 'dsgCapSum',
            //         dataKey: 'dsgCapSum',
            //         width: locale.value == 'en' ? 182 : 120,
            //     },
            //     {
            //         value: 'volMax',
            //         title: t('vol_max') + '(V)',
            //         key: 'volMax',
            //         dataKey: 'volMax',
            //         width: locale.value == 'en' ? 152 : 140,
            //         // cellRenderer: ({ cellData: volMax }) =>
            //         //     new Decimal(volMax).dividedBy(1000),
            //     },
            //     {
            //         value: 'volMaxId',
            //         title: t('Max voltage position'),
            //         key: 'volMaxId',
            //         dataKey: 'volMaxId',
            //         width: locale.value == 'en' ? 152 : 140,
            //     },
            //     {
            //         value: 'volMin',
            //         title: t('vol_min') + '(V)',
            //         key: 'volMin',
            //         dataKey: 'volMin',
            //         width: locale.value == 'en' ? 150 : 140,
            //         // cellRenderer: ({ cellData: volMin }) =>
            //         //     new Decimal(volMin).dividedBy(1000),
            //     },
            //     {
            //         value: 'volMinId',
            //         title: t('Min voltage position'),
            //         key: 'volMinId',
            //         dataKey: 'volMinId',
            //         width: locale.value == 'en' ? 152 : 140,
            //     },
            //     {
            //         value: 'volDif',
            //         title: t('vol_dif') + '(V)',
            //         key: 'volDif',
            //         dataKey: 'volDif',
            //         width: locale.value == 'en' ? 168 : 140,
            //     },
            //     {
            //         value: 'volAvg',
            //         title: t('Average voltage') + '(V)',
            //         key: 'volAvg',
            //         dataKey: 'volAvg',
            //         width: locale.value == 'en' ? 152 : 140,
            //     },
            //     {
            //         value: 'tempMax',
            //         title: t('temp_max') + '(℃)',
            //         key: 'tempMax',
            //         dataKey: 'tempMax',
            //         width: locale.value == 'en' ? 148 : 140,
            //     },
            //     {
            //         value: 'tempMaxId',
            //         title: t('Max temperature position'),
            //         key: 'tempMaxId',
            //         dataKey: 'tempMaxId',
            //         width: locale.value == 'en' ? 188 : 140,
            //     },
            //     {
            //         value: 'tempMin',
            //         title: t('temp_min') + '(℃)',
            //         key: 'tempMin',
            //         dataKey: 'tempMin',
            //         width: locale.value == 'en' ? 148 : 140,
            //     },
            //     {
            //         value: 'tempMinId',
            //         title: t('Min temperature position'),
            //         key: 'tempMinId',
            //         dataKey: 'tempMinId',
            //         width: locale.value == 'en' ? 188 : 140,
            //     },
            //     {
            //         value: 'tempDif',
            //         title: t('Temp difference') + '(℃)',
            //         key: 'tempDif',
            //         dataKey: 'tempDif',
            //         width: locale.value == 'en' ? 154 : 140,
            //     },
            //     {
            //         value: 'tempAvg',
            //         title: t('device_pingjunwendu') + '(℃)',
            //         key: 'tempAvg',
            //         dataKey: 'tempAvg',
            //         width: 120,
            //     },
            //     {
            //         value: 'insZ',
            //         title: t('ins_p') + '(kΩ)',
            //         key: 'insZ',
            //         dataKey: 'insZ',
            //         width: locale.value == 'en' ? 168 : 140,
            //     },
            //     {
            //         value: 'insF',
            //         title: t('ins_n') + '(kΩ)',
            //         key: 'insF',
            //         dataKey: 'insF',
            //         width: locale.value == 'en' ? 172 : 140,
            //     },
            //     {
            //         value: 'alarmSta',
            //         title: t('Alarm status'),
            //         key: 'alarmSta',
            //         dataKey: 'alarmSta',
            //         width: 120,
            //         cellRenderer: ({ cellData: alarmSta }) => {
            //             return alarmSta == 1 ? ' 异常' : '正常'
            //         },
            //     },
            //     {
            //         value: 'alarmNub',
            //         title: t('Number of alarms'),
            //         key: 'alarmNub',
            //         dataKey: 'alarmNub',
            //         width: locale.value == 'en' ? 140 : 140,
            //     },
            //     {
            //         value: 'heartbeat',
            //         title: t('Heartbeat status'),
            //         key: 'heartbeat',
            //         dataKey: 'heartbeat',
            //         width: locale.value == 'en' ? 140 : 140,
            //         // cellRenderer: ({ cellData: heartbeat }) => {
            //         //     return heartbeat == 1 ? '开' : '关'
            //         // },
            //     },
            //     {
            //         value: 'highVolSta',
            //         title: t('High voltage detection'),
            //         key: 'highVolSta',
            //         dataKey: 'highVolSta',
            //         width: locale.value == 'en' ? 170 : 140,
            //     },
            //     {
            //         value: 'realySta',
            //         title: t('Relay Status'),
            //         key: 'realySta',
            //         dataKey: 'realySta',
            //         width: 120,
            //         // cellRenderer: ({ cellData: realySta }) => {
            //         //     return formatterStatus({ chargeStatus: realySta })
            //         // },
            //     },
            //     {
            //         value: 'cc2Res',
            //         title: t('CC2 resistance'),
            //         key: 'cc2Res',
            //         dataKey: 'cc2Res',
            //         width: 120,
            //     },

            //     {
            //         value: 'chgerReqCurr',
            //         title: t('Charge request current') + '(A)',
            //         key: 'chgerReqCurr',
            //         dataKey: 'chgerReqCurr',
            //         width: 150,
            //     },
            //     {
            //         value: 'chgerReqVolt',
            //         title: t('Charge Request voltage') + '(V)',
            //         key: 'chgerReqVolt',
            //         dataKey: 'chgerReqVolt',
            //         width: 150,
            //     },
            //     {
            //         value: 'inputSignalSta',
            //         title: t('Input signal status'),
            //         key: 'inputSignalSta',
            //         dataKey: 'inputSignalSta',
            //         width: 150,
            //     },
            //     {
            //         value: 'signal4g',
            //         title: t('4G signal'),
            //         key: 'signal4g',
            //         dataKey: 'signal4g',
            //         width: 120,
            //     },
            //     {
            //         value: 'longitude',
            //         title: t('Reserved longitude'),
            //         key: 'longitude',
            //         dataKey: 'longitude',
            //         width: 150,
            //     },
            //     {
            //         value: 'latitude',
            //         title: t('Reserved latitude'),
            //         key: 'latitude',
            //         dataKey: 'latitude',
            //         width: 150,
            //     },
            //     {
            //         value: 'lockSta',
            //         title: t('Lock status'),
            //         key: 'lockSta',
            //         dataKey: 'lockSta',
            //         width: 150,
            //         cellRenderer: ({ cellData: lockSta }) => {
            //             return lockSta == 0
            //                 ? t('status_zhengchang')
            //                 : t('locked')
            //         }, // 1:锁住 0:未锁
            //     },
            //     {
            //         value: 'bmsModel',
            //         title: t('Bms model'),
            //         key: 'bmsModel',
            //         dataKey: 'bmsModel',
            //         width: 150,
            //     },
            //     {
            //         value: 'sysPower',
            //         title: t('Total power'),
            //         key: 'sysPower',
            //         dataKey: 'sysPower',
            //         width: 150,
            //     },
            //     {
            //         value: 'sysEnergy',
            //         title: t('Total Energy'),
            //         key: 'sysEnergy',
            //         dataKey: 'sysEnergy',
            //         width: 150,
            //     },
            //     {
            //         value: 'mosTemp',
            //         title: t('MOS temperature') + '(℃)',
            //         key: 'mosTemp',
            //         dataKey: 'mosTemp',
            //         width: 150,
            //     },
            //     {
            //         value: 'envTemp',
            //         title: t('envTemp') + '(℃)',
            //         key: 'envTemp',
            //         dataKey: 'envTemp',
            //         width: 150,
            //     },
            //     {
            //         value: 'heatTemp',
            //         title: t('heatTemp') + '(℃)',
            //         key: 'heatTemp',
            //         dataKey: 'heatTemp',
            //         width: 150,
            //     },
            //     {
            //         value: 'heatCur',
            //         title: t('heatCur') + '(℃)',
            //         key: 'heatCur',
            //         dataKey: 'heatCur',
            //         width: 150,
            //     },
            //     {
            //         value: 'cycleCount',
            //         title: t('Cycle times'),
            //         key: 'cycleCount',
            //         dataKey: 'cycleCount',
            //         width: 150,
            //     },
            //     {
            //         value: 'chgTimeSum',
            //         title: t('charging time') + '(h)',
            //         key: 'chgTimeSum',
            //         dataKey: 'chgTimeSum',
            //         width: 150,
            //     },
            //     {
            //         value: 'dsgTimeSum',
            //         title: t('Discharging Time') + '(h)',
            //         key: 'dsgTimeSum',
            //         dataKey: 'dsgTimeSum',
            //         width: 150,
            //     },
            //     {
            //         value: 'balancingMode',
            //         title: t('Balanced mode'),
            //         key: 'balancingMode',
            //         dataKey: 'balancingMode',
            //         width: 150,
            //     },
            //     {
            //         value: 'balancingCur',
            //         title: t('Balance current'),
            //         key: 'balancingCur',
            //         dataKey: 'balancingCur',
            //         width: 150,
            //     },
            //     {
            //         value: 'balancingFlag1',
            //         title: t('Balanced logo') + '1',
            //         key: 'balancingFlag1',
            //         dataKey: 'balancingFlag1',
            //         width: 150,
            //     },
            //     {
            //         value: 'balancingFlag2',
            //         title: t('Balanced logo') + '2',
            //         key: 'balancingFlag2',
            //         dataKey: 'balancingFlag2',
            //         width: 150,
            //     },
            //     {
            //         value: 'balancingFlag3',
            //         title: t('Balanced logo') + '3',
            //         key: 'balancingFlag3',
            //         dataKey: 'balancingFlag3',
            //         width: 150,
            //     },
            //     {
            //         value: 'balancingFlag4',
            //         title: t('Balanced logo') + '4',
            //         key: 'balancingFlag4',
            //         dataKey: 'balancingFlag4',
            //         width: 150,
            //     },
            //     {
            //         value: 'faultCode',
            //         title: t('Fault Code'),
            //         key: 'faultCode',
            //         dataKey: 'faultCode',
            //         width: 150,
            //     },
            //     {
            //         value: 'faultFlag1',
            //         title: t('faultFlag') + '1',
            //         key: 'faultFlag1',
            //         dataKey: 'faultFlag1',
            //         width: 150,
            //     },
            //     {
            //         value: 'faultFlag2',
            //         title: t('faultFlag') + '2',
            //         key: 'faultFlag2',
            //         dataKey: 'faultFlag2',
            //         width: 150,
            //     },
            //     {
            //         value: 'ins',
            //         title: t('ins'),
            //         key: 'ins',
            //         dataKey: 'ins',
            //         width: 150,
            //     },
            //     {
            //         value: 'ccRes',
            //         title: t('CC resistance'),
            //         key: 'ccRes',
            //         dataKey: 'ccRes',
            //         width: 150,
            //     },
            //     {
            //         value: 'cpPwm',
            //         title: t('CP duty cycle'),
            //         key: 'cpPwm',
            //         dataKey: 'cpPwm',
            //         width: 150,
            //     },
            //     {
            //         value: 'cpFreq',
            //         title: t('cp_freq'),
            //         key: 'cpFreq',
            //         dataKey: 'cpFreq',
            //         width: 150,
            //     },
            //     {
            //         value: 'chgerCommuSta',
            //         title: t('Charging communication status'),
            //         key: 'chgerCommuSta',
            //         dataKey: 'chgerCommuSta',
            //         width: 150,
            //     },
            //     {
            //         value: 'chgerOutVolt',
            //         title: t('chger_out_volt') + '(V)',
            //         key: 'chgerOutVolt',
            //         dataKey: 'chgerOutVolt',
            //         width: 150,
            //     },
            //     {
            //         value: 'chgerOutCurr',
            //         title: t('chger_out_curr') + '(A)',
            //         key: 'chgerOutCurr',
            //         dataKey: 'chgerOutCurr',
            //         width: 150,
            //     },
            //     {
            //         value: 'chgerFaultSta',
            //         title: t('Charger fault status'),
            //         key: 'chgerFaultSta',
            //         dataKey: 'chgerFaultSta',
            //         width: 150,
            //     },
            //     {
            //         value: 'vccVolt',
            //         title: t('vccVolt'),
            //         key: 'vccVolt',
            //         dataKey: 'vccVolt',
            //         width: 150,
            //     },
            //     {
            //         value: 'keyOnVolt',
            //         title: t('keyOnVolt'),
            //         key: 'keyOnVolt',
            //         dataKey: 'keyOnVolt',
            //         width: 150,
            //     },
            //     {
            //         value: 'hv1',
            //         title: t('hv') + '1',
            //         key: 'hv1',
            //         dataKey: 'hv1',
            //         width: 150,
            //     },
            //     {
            //         value: 'hv2',
            //         title: t('hv') + '2',
            //         key: 'hv2',
            //         dataKey: 'hv2',
            //         width: 150,
            //     },
            //     {
            //         value: 'hv3',
            //         title: t('hv') + '3',
            //         key: 'hv3',
            //         dataKey: 'hv3',
            //         width: 150,
            //     },
            //     {
            //         value: 'hv4',
            //         title: t('hv') + '4',
            //         key: 'hv4',
            //         dataKey: 'hv4',
            //         width: 150,
            //     },
            //     {
            //         value: 'hv5',
            //         title: t('hv') + '5',
            //         key: 'hv5',
            //         dataKey: 'hv5',
            //         width: 150,
            //     },
            //     {
            //         value: 'hv6',
            //         title: t('hv') + '6',
            //         key: 'hv6',
            //         dataKey: 'hv6',
            //         width: 150,
            //     },
            // ]
            // console.log(newHeader, 1, 1, 1)
            //   selectedFields:选中的值。
            selectedFields.value = showFields.value.map((e) => e.value)
        }
        const otherTableColumn = computed(() => {
            return showFields.value.filter((item, index) => {
                if (selectedFields.value.includes(item.value)) return item
            })
        })
        const onSearchOther = async (flag) => {
            tableLoading.value = true
            await getBmsDataTable()
            let firstItem = allOtherTableData.value[0]
            otherTableData.value = allOtherTableData.value.map((item) => {
                const newItem = {
                    ...item,
                }
                // 电压温度数组转独立属性(voltage1 ~ voltage16)
                if (Array.isArray(firstItem.temperature)) {
                    item.temperature.forEach((value, index) => {
                        newItem[`t${index + 1}`] = value
                    })
                }
                if (Array.isArray(firstItem.voltage)) {
                    item.voltage.forEach((value, index) => {
                        newItem[`v${index + 1}`] = value
                    })
                }
                return newItem
            })
            // })
            tableLoading.value = false
        }
        const loadMoreLoading = ref(false)
        const loadMore = async () => {
            loadMoreLoading.value = true
            await onSearchOther(true)
            loadMoreLoading.value = false
        }
        const activeName = ref('a')
        const basicInfoData = ref()
        const getBasicInfo = async () => {
            //
            try {
                const {
                    data: { data, code },
                } = await powerApi.getBasicInfo({
                    sn: currentDeviceSn.value,
                })
                if (code === 0) {
                    basicInfoData.value = data
                    basicInfoData.value['stationPic'] = defaultImg
                }
            } catch (error) {
                basicInfoData.value.stationPic = defaultImg
            }
        }
        const realData = ref()
        const getRealInfo = async () => {
            let res = await powerApi.getRealData({ sn: currentDeviceSn.value })
            realData.value = res.data.data
            realData.value.sn = res.data.data.sn || currentDeviceSn.value
        }
        const getRealData = async () => {
            // 实时数据
            await getRealCells() // 实时热力分布
            await getDistributionDataV() // 极差方差
            updateAnalysisChart()
            // updateAnalysisChartF()
        }
        const selectedChargeStatus = ref('')
        const chargeStatusOptions = ref([
            {
                label: t('All'),
                value: '',
            },
            {
                label: t('status_chongdian'),
                value: '1',
            },
            {
                label: t('status_fangdian'),
                value: '2',
            },
        ])

        const handleChangeChargeStatus = () => {
            getRunningData()
        }
        const runningDataRangeDate = ref({
            startDate: moment().subtract(30, 'day').format('YYYY-MM-DD'),
            endDate: moment().format('YYYY-MM-DD'),
        })
        const runningDataRangeDate24 = ref(moment().format('YYYY-MM-DD'))
        const runningDataPageInfo = ref({
            current: 1,
            size: 10,
        })
        const runningDataPageTotal = ref(0)
        const runningTableData = ref([])
        const runningDataTableColumn = ref([
            {
                title: t('station_yunxingzhuangtai'),
                dataIndex: 'chargeStatus',
                key: 'chargeStatus',
            },
            {
                title: t('common_kaishishijian'),
                dataIndex: 'startTime',
                key: 'startTime',
            },
            {
                title: t('common_jieshushijian'),
                dataIndex: 'endTime',
                key: 'endTime',
            },
            {
                title: t('Charge/discharge capacity') + '(Ah)',
                dataIndex: 'chargeAndDischargeQuantity',
                key: 'chargeAndDischargeQuantity',
                width: 240,
            },
            {
                title: t('Initial SOC'),
                dataIndex: 'startSoc',
                key: 'startSoc',
                width: 120,
            },
            {
                title: t('Final SOC'),
                dataIndex: 'endSoc',
                key: 'endSoc',
                width: 120,
            },
            {
                title: t('This time duration') + '(h)',
                dataIndex: 'duration',
                key: 'duration',
            },
            {
                title: t('duration') + '(h)',
                dataIndex: 'totalDuration',
                key: 'totalDuration',
                width: 240,
            },
        ])
        const formatterStatus = (e) => {
            return t(getState(e.chargeStatus, 'power').label)
        }
        const time24H = ref([])
        const s124H = ref([])
        const s224H = ref([])
        const getRunningData = async () => {
            if (changeViewRunningType.value == 'chart') {
                let res1 = await powerApi.getStatsDeviceSocAndWorkStatusByDay({
                    day: runningDataRangeDate24.value,
                    sn: currentDeviceSn.value,
                })
                const colors = [
                    'rgba(0,0,0,0)',
                    isDark.value
                        ? 'rgba(30, 204, 153, 0.3)'
                        : 'rgba(30, 204, 153, 0.1)',
                    isDark.value
                        ? 'rgba(253, 117, 11, 0.3)'
                        : 'rgba(253, 117, 11, 0.1)',
                ]
                time24H.value = res1.data.data.map((item) => {
                    return dayjs(item.time).format('HH:mm')
                })
                time24H.value.push('24:00')
                echartsData24H.value = res1.data.data.map((item) => item.soc)
                echartsData24H.value.push(0)
                s124H.value = res1.data.data.map((item) => item.soc)
                s124H.value.push(0)
                s224H.value = res1.data.data.map((item) => {
                    return {
                        value: 100,
                        itemStyle: { color: colors[item.batteryStatus] },
                    }
                })
                initDom()
            } else {
                let params = {
                    ...runningDataPageInfo.value,
                    startDate: runningDataRangeDate.value.startDate,
                    endDate: runningDataRangeDate.value.endDate,
                    sn: currentDeviceSn.value,
                    chargeStatus: selectedChargeStatus.value,
                }
                let res = await powerApi.getPowerBmsChargeRecordPageList(params)
                runningTableData.value = res.data.data.records
                runningDataPageTotal.value = res.data.data.total
            }
        }
        const runningDataPageChange = () => {}
        const handleRunningDataCurrentChange = () => {
            getRunningData()
        }
        const onSearchRunningTableData = () => {
            runningDataPageInfo.value.current = 1
            getRunningData()
        }

        const handleTabChange = async (e) => {
            await getRealInfo() // 实时基础信息
            if (e == 'a') {
                // 基础信息tab
                await getBasicInfo() // 基础信息
                if (totalAnalysisName.value == 'a') {
                    await getstatsPowerBattDailyUsage() // 设备状态统计tab
                } else {
                    await handleChargeAnalysisDateChange()
                }
            } else if (e == 'b') {
                // 实时状态数据
                await getRealData()
            } else if (e == 'c') {
                showMore.value = undefined
                if (activeHistoryName.value == 'b') {
                    if (chargeViewType.value == 'chart') {
                        onChartsDateChange()
                    } else {
                        if (allOtherTableData.value.length) {
                            //
                        } else {
                            onSearchOther(false)
                        }
                    }
                } else {
                    await getRunningData()
                }
            }
            //
        }
        const onOpenPopover = async () => {
            deviceSearchKeyword.value = ''
            await getDevicePage(true)
        }
        const popoverRef = ref()
        const onClickOutside = () => {
            unref(popoverRef).popperRef?.delayHide?.()
        }
        const onSelectDevice = async (item) => {
            currentDeviceSn.value = item.sn
            await getRealInfo() // 实时基础信息
            await getBmsDataColumn() // 获取表头
            if (activeName.value == 'a') {
                await getBasicInfo() // 基础信息
                if (totalAnalysisName.value == 'a') {
                    await getstatsPowerBattDailyUsage() // 设备状态统计tab
                } else {
                    await handleChargeAnalysisDateChange()
                }
            } else if (activeName.value == 'b') {
                //
                await getRealData()
            } else if (activeName.value == 'c') {
                showMore.value = undefined
                otherTableData.value = []
                allOtherTableData.value = []
                if (activeHistoryName.value == 'b') {
                    if (chargeViewType.value == 'chart') {
                        onChartsDateChange()
                    } else {
                        if (allOtherTableData.value.length) {
                            //
                        } else {
                            onSearchOther(false)
                        }
                    }
                } else {
                    await getRunningData()
                }
            }
            popoverRef.value?.hide()
        }
        const activeHistoryName = ref('a')
        const handleActiveHistoryTabChange = (e) => {
            if (e == 'a') {
                //
                getRunningData()
            } else {
                if (chargeViewType.value == 'chart') {
                    onChartsDateChange()
                } else {
                    if (allOtherTableData.value.length) {
                        //
                    } else {
                        onSearchOther(false)
                    }
                }
            }
        }
        const analysisActiveName = ref('a')
        const handleAnalysisTabChange = () => {
            //

            if (analysisActiveName.value == 'a') {
                nextTick(() => {
                    setChargeTimeDistributionChart()
                })
            } else if (analysisActiveName.value == 'b') {
                nextTick(() => {
                    setChargeStartSocDistributionChart()
                })
            } else if (analysisActiveName.value == 'c') {
                nextTick(() => {
                    setChargeCapacityDistributionChart()
                })
            } else {
                setChargeTimeDistributionChart()
                setChargeStartSocDistributionChart()
                setChargeCapacityDistributionChart()
            }
        }
        const currentDeviceSn = ref()

        const deviceStatusActiveName = ref('a')
        const getstatsPowerBattDailyUsage = async () => {
            //
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            let params = {}
            let res = await powerApi.statsPowerBattDailyUsage({
                ...params,
                sn: currentDeviceSn.value,
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
                ...chargeTotalDateType.value,
            })
            statsPowerBattDailyUsageData.value = res.data.data || []
            await handleDeviceStatusTabChange()
        }
        const handleDeviceStatusTabChange = async () => {
            // 设备状态统计tab
            if (deviceStatusActiveName.value == 'a') {
                setDeviceStatusStatisticsCharts('chargeTotalByDate')
            } else if (deviceStatusActiveName.value == 'b') {
                setDeviceStatusStatisticsCharts('runningDuration')
            }
        }
        const chargeTotalDateType = ref()
        const chargeTotalDateOptions = ref([
            {
                label: t('Last Week'),
                value: '1',
            },
            {
                label: t('Last Year'),
                value: '2',
            },
        ])
        const onChargeTotalDateChange = async () => {
            //
            await getstatsPowerBattDailyUsage()
        }
        const setDeviceStatusStatisticsCharts = (id) => {
            if (!statsPowerBattDailyUsageData.value?.length) {
                return
            }
            let x
            x = statsPowerBattDailyUsageData.value.map((item) => item.date)
            if (chargeTotalDateType.value.periodType == 'day') {
                x = statsPowerBattDailyUsageData.value.map((item) =>
                    item.date.slice(5)
                )
            }

            const y1 = statsPowerBattDailyUsageData.value.map(
                (item) => item.chargeDur
            )
            const y2 = statsPowerBattDailyUsageData.value.map(
                (item) => item.dischargeDur
            )
            const y3 = statsPowerBattDailyUsageData.value.map(
                (item) => item.chargeCap
            )
            const y4 = statsPowerBattDailyUsageData.value.map(
                (item) => item.dischargeCap
            )
            let options = _cloneDeep(getChargeOption())
            options.legend.top = '12px'
            options.grid.bottom = '10px'
            options.grid.right = 10
            if (x.length >= 20) {
                options.series[0].barWidth = '12px'
                options.series[1].barWidth = '12px'
            } else if (x.length >= 10) {
                options.series[0].barWidth = '25px'
                options.series[1].barWidth = '25px'
            } else {
                options.series[0].barWidth = '30px'
                options.series[1].barWidth = '30px'
            }
            options.xAxis.data = x
            options.yAxis.name = id == 'runningDuration' ? 'h' : 'Ah'
            options.series[0].data = y3
            options.series[1].data = y4
            options.tooltip.formatter = function (params) {
                return (
                    params[0].name +
                    '<br/>' +
                    params[0].marker +
                    params[0].seriesName +
                    ' : ' +
                    (params[0].value || 0) +
                    'Ah' +
                    '<br/>' +
                    params[1].marker +
                    params[1].seriesName +
                    ' : ' +
                    (params[1].value || 0) +
                    'Ah'
                )
            }
            if (id == 'runningDuration') {
                options.series[0].data = y1
                options.series[1].data = y2
                options.series[0].name = t('Charging duration')
                options.series[1].name = t('Discharging duration')
                options.legend.data = [
                    t('Charging duration'),
                    t('Discharging duration'),
                ]
                options.tooltip.formatter = function (params) {
                    return (
                        params[0].name +
                        '<br/>' +
                        params[0].marker +
                        params[0].seriesName +
                        ' : ' +
                        (params[0].value || 0) +
                        'h' +
                        '<br/>' +
                        params[1].marker +
                        params[1].seriesName +
                        ' : ' +
                        (params[1].value || 0) +
                        'h'
                    )
                }
            }

            updateEcharts(id, options)
        }
        const statsPowerBattDailyUsageData = ref([])

        const distributionData = reactive({
            minutes: [],
            voltageRanges: [],
            voltageVariances: [],
            tempRanges: [],
            tempVariances: [],
        })
        const getDistributionDataV = async () => {
            //
            let params = {
                startDate: moment().format('YYYY-MM-DD'),
                endDate: moment().format('YYYY-MM-DD'),
                sn: currentDeviceSn.value,
            }
            let res = await powerApi.getCellRangeAndVariance(params)

            // 1. 将对象转换为排序后的键值对数组
            const sortedEntries = Object.entries(res.data.data).sort(
                ([a], [b]) => a.localeCompare(b)
            )

            // 2. 初始化结果数组
            distributionData.minutes = sortedEntries.map(
                (item) => item[0].split(' ')[1]
            )
            distributionData.voltageRanges = sortedEntries.map(
                (item) => item[1]?.voltageRange || 0
            )
            distributionData.voltageVariances = sortedEntries.map(
                (item) => item[1]?.voltageVariance || 0
            )
            distributionData.tempRanges = sortedEntries.map(
                (item) => item[1]?.tempRange || 0
            )
            distributionData.tempVariances = sortedEntries.map(
                (item) => item[1]?.tempVariance || 0
            )
            // 最终结果
        }
        const activeDistributionName = ref('a')
        const transformData3 = (data, yCount) => {
            // 验证数据总量是否匹配
            const result = []
            let xCount = Math.floor(data.length / yCount)
            if (data.length !== xCount * yCount) {
                throw new Error('数据总量与坐标数量不匹配')
            }

            // 按y轴数量分割数据
            for (let y = 0; y < yCount; y++) {
                // 计算当前y轴数据切片范围
                const start = y * xCount
                const end = start + xCount
                const yData = data.slice(start, end)
                // 生成坐标数据
                yData.forEach((value, x) => {
                    result.push([x, y, value])
                })
            }

            return result
        }
        const updateDistributionChart = () => {
            let options = _cloneDeep(realTimeDistributedOptions())
            let parallels = 2
            const yData = Array.from(
                { length: 2 },
                (_, index) => '' // 存在则取值，不存在则默认为0
            )
            options.yAxis.data = yData
            if (!realCellsData.value) {
                updateEcharts(
                    'thermalDistribution',
                    realTimeDistributedOptions()
                )
                return
            }
            options.tooltip.formatter = function (params) {
                if (locale.value == 'en') {
                    return (
                        'Battery Cell: ' +
                        (params.dataIndex + 1) +
                        '<br />' +
                        params.marker +
                        '    ' +
                        params.value[2] +
                        ' V'
                    )
                } else {
                    return (
                        params.dataIndex +
                        1 +
                        '号电芯' +
                        '<br />' +
                        params.marker +
                        '    ' +
                        params.value[2] +
                        ' V'
                    )
                }
            }
            if (activeDistributionName.value == 'a') {
                let newVs = realCellsData.value.voltage.map((item) => {
                    return item
                })
                let min = Math.min(...newVs)
                let max = Math.max(...newVs)
                const res = transformData3(newVs, parallels)
                options.visualMap.min = min
                options.visualMap.max = max
                options.series[0].data = res
                options.series[0].name = '电压(V)'
            } else if (activeDistributionName.value == 'b') {
                let min = Math.min(...realCellsData.value.temperature)
                let max = Math.max(...realCellsData.value.temperature)
                const res1 = transformData3(
                    realCellsData.value.temperature,
                    parallels
                )
                options.visualMap.min = min
                options.visualMap.max = max
                options.series[0].data = res1
                options.series[0].name = '温度(°C)'
                options.gradientColor = ['#F3E49F', '#EAC391', '#C45054']
                options.tooltip.formatter = function (params) {
                    if (locale.value == 'en') {
                        return (
                            'Temperature Sensor: ' +
                            (params.dataIndex + 1) +
                            '<br />' +
                            params.marker +
                            '    ' +
                            params.value[2] +
                            ' °C'
                        )
                    } else {
                        return (
                            params.dataIndex +
                            1 +
                            '号温度传感器' +
                            '<br />' +
                            params.marker +
                            '    ' +
                            params.value[2] +
                            ' °C'
                        )
                    }
                }
            }
            updateEcharts('thermalDistribution', options)
        }
        const handleActiveDistributionTabChange = (e) => {
            updateDistributionChart()
            if (e == 'a') {
                updateAnalysisChart()
            } else {
                updateAnalysisChart('temp')
            }
        }
        const getMax = (arr) => {
            let res = arr.reduce((currentMax, num) => {
                if (num == null) return currentMax // 跳过 null/undefined
                return Math.max(currentMax, num)
            }, -Infinity)
            return res
        }

        // 处理科学计数法数字的函数，转换为正常小数显示
        const formatScientificNotation = (data) => {
            if (!Array.isArray(data)) return data
            return data.map((value) => {
                if (value === null || value === undefined) return value
                // 检查是否为科学计数法格式的数字
                if (typeof value === 'number' || typeof value === 'string') {
                    const num = Number(value)
                    if (!isNaN(num)) {
                        // 对于极小的数字，使用足够的小数位数来完整显示
                        if (Math.abs(num) < 0.001 && num !== 0) {
                            // 使用8位小数来避免科学计数法，确保小数完整显示
                            return Number(num.toFixed(8))
                        }
                        // 对于正常范围的数字，保留适当的小数位数
                        return Number(num.toFixed(4))
                    }
                }
                return value
            })
        }

        const updateAnalysisChart = (type) => {
            let options = _cloneDeep(getElectricityData())
            let options1 = _cloneDeep(getElectricityData())
            options.xAxis.data = distributionData.minutes
            options.yAxis.data = [moment().format('YYYY-MM-DD')]
            options1.xAxis.data = distributionData.minutes
            options1.yAxis.data = [moment().format('YYYY-MM-DD')]

            // 处理科学计数法数据
            options.series[0].data = formatScientificNotation(
                distributionData.voltageRanges
            )
            options1.series[0].data = formatScientificNotation(
                distributionData.voltageVariances
            )

            // 自定义tooltip格式化函数，显示正常小数格式
            const customTooltipFormatter = (params) => {
                if (Array.isArray(params)) {
                    const param = params[0]
                    const value = param.value
                    let formattedValue = value

                    if (typeof value === 'number') {
                        // 对于所有数值都使用小数显示，不使用科学计数法
                        if (Math.abs(value) < 0.001 && value !== 0) {
                            formattedValue = value.toFixed(8)
                        } else if (Math.abs(value) < 1) {
                            formattedValue = value.toFixed(6)
                        } else {
                            formattedValue = value.toFixed(4)
                        }
                    }

                    const unit = type === 'temp' ? '°C' : 'mV'
                    return `${param.name}<br/>${param.marker}${formattedValue} ${unit}`
                }
                return ''
            }

            // 应用自定义tooltip
            options.tooltip.formatter = customTooltipFormatter
            options1.tooltip.formatter = customTooltipFormatter

            // 自定义Y轴标签格式化，显示正常小数格式
            const yAxisFormatter = (value) => {
                if (typeof value === 'number') {
                    if (Math.abs(value) < 0.001 && value !== 0) {
                        // 对于极小数值也使用小数显示，不使用科学计数法
                        return value.toFixed(6)
                    } else if (Math.abs(value) < 1) {
                        return value.toFixed(4)
                    } else {
                        return value.toFixed(3)
                    }
                }
                return value
            }

            options.yAxis.axisLabel.formatter = yAxisFormatter
            options1.yAxis.axisLabel.formatter = yAxisFormatter

            let max, max1
            max = getMax(distributionData.voltageRanges)
            max1 = getMax(distributionData.voltageVariances)
            if (type == 'temp') {
                options.series[0].data = formatScientificNotation(
                    distributionData.tempRanges
                )
                options1.series[0].data = formatScientificNotation(
                    distributionData.tempVariances
                )
                max = getMax(distributionData.tempRanges)
                max1 = getMax(distributionData.tempVariances)
                options.yAxis.name = t('Temp') + '/°C'
                options1.yAxis.name = t('Temp') + '/°C'
                max = Math.max(...distributionData.tempRanges)
            } else {
                options.yAxis.name = t('unit_danwei') + '/mv'
                options1.yAxis.name = t('unit_danwei') + '/mv'
            }
            options.series[0].connectNulls = false
            options1.series[0].connectNulls = false
            options.dataZoom[0].height = 15
            options.dataZoom[0].bottom = 10
            options.grid['bottom'] = '50px'
            options1.dataZoom[0].height = 15
            options1.dataZoom[0].bottom = 10
            options1.grid['bottom'] = '50px'
            updateEcharts('analysisChartJ', options)
            updateEcharts('analysisChartF', options1)
        }

        const realCellsData = ref()
        const getRealCells = async () => {
            let res = await powerApi.getRealCells({ sn: currentDeviceSn.value })
            //
            realCellsData.value = res.data.data

            updateDistributionChart()
        }
        const vehicleTypes = computed(
            () => store.state.dictionary.dictionaries.vehicleType || []
        )
        const getVehicleType = (val) => {
            if (vehicleTypes.value.length == 0) return
            if (!val) return '-'
            return vehicleTypes.value.find((item) => item.value == val).label
        }

        const changeViewRunningType = ref('chart')
        const onChangeViewRunning = (e) => {
            getRunningData()
        }

        // ##新接口:

        // 统计分析
        const totalAnalysisName = ref('a')
        const handleTabChangeTotalAnalysi = async () => {
            if (totalAnalysisName.value === 'a') {
                await handleDeviceStatusTabChange() // 更新a的charts
            } else if (totalAnalysisName.value === 'b') {
                // 获取数据并渲染图表
                await handleChargeAnalysisDateChange()
            }
        }

        // 添加状态变量
        const chargeAnalysisDateRange = ref({
            startDate: moment().subtract(30, 'day').format('YYYY-MM-DD'),
            endDate: moment().format('YYYY-MM-DD'),
        })

        // 初始化图表实例
        const setChargeTimeDistributionChart = () => {
            const chargeTimeChart = echarts.init(
                document.getElementById('chargeTimeDistribution')
            )
            const options = _cloneDeep(analysisdOptions())
            options.xAxis.name = t('time (h)')
            options.yAxis.name = t('charging time(h)')
            const xData = chargeTimeDistributionData.value.map(
                (item) => item.hour
            )
            const yData = chargeTimeDistributionData.value.map(
                (item) => item.count
            )
            options.xAxis.data = xData
            options.series[0].data = yData
            nextTick(() => {
                chargeTimeChart?.setOption(options)
            })
        }
        // 获取充电时段分布数据
        const chargeTimeDistributionData = ref()
        const getChargeTimeDistribution = async (params) => {
            try {
                const res = await powerApi.statsDeviceChargeTimeDistribution(
                    params
                )
                chargeTimeDistributionData.value = res.data.data || []
                setChargeTimeDistributionChart()
            } catch (error) {
                console.error('获取充电时段分布数据失败:', error)
            }
        }

        const setChargeStartSocDistributionChart = () => {
            const chargeSocChart = echarts.init(
                document.getElementById('chargeStartSocDistribution')
            )
            const options = _cloneDeep(analysisdOptions())
            options.xAxis.name = t('Battery capacity')
            options.yAxis.name = t('Charging times')
            const xData = chargeStartSocDistributionData.value.map(
                (item) => item.soc
            )
            const yData = chargeStartSocDistributionData.value.map(
                (item) => item.count
            )
            options.xAxis.data = xData
            options.series[0].data = yData
            nextTick(() => {
                chargeSocChart?.setOption(options)
            })
        }
        // 获取充电起始SOC分布数据
        const chargeStartSocDistributionData = ref()
        const getChargeStartSocDistribution = async (params) => {
            try {
                const res =
                    await powerApi.statsDeviceChargeStartSocDistribution(params)
                chargeStartSocDistributionData.value = res.data.data
                setChargeStartSocDistributionChart()
            } catch (error) {
                console.error('获取充电起始SOC分布数据失败:', error)
            }
        }

        const setChargeCapacityDistributionChart = () => {
            const chargeCapacityChart = echarts.init(
                document.getElementById('chargeCapacityDistribution')
            )
            const options = _cloneDeep(analysisdOptions())
            options.xAxis.name = t('Capacity') + '(Ah)'
            options.yAxis.name = t('Quantity')
            options.grid.right = '100px'
            const xData = chargeCapacityDistributionData.value.map(
                (item) => item.chgCapacity
            )
            const yData = chargeCapacityDistributionData.value.map(
                (item) => item.count
            )
            options.xAxis.data = xData
            options.series[0].data = yData
            nextTick(() => {
                chargeCapacityChart?.setOption(options)
            })
        }
        // 获取单次充电容量分布数据
        const chargeCapacityDistributionData = ref()
        const getChargeCapacityDistribution = async (params) => {
            try {
                const res =
                    await powerApi.statsDeviceChargeCapacityDistribution(params)
                chargeCapacityDistributionData.value = res.data.data
                setChargeCapacityDistributionChart()
            } catch (error) {
                console.error('获取单次充电容量分布数据失败:', error)
            }
        }

        // 处理日期变化
        const handleChargeAnalysisDateChange = async () => {
            if (
                !customerDetail.value.projectId &&
                !customerDetail.value.customerId
            ) {
                return
            }
            const params = {
                startDate: chargeAnalysisDateRange.value.startDate,
                endDate: chargeAnalysisDateRange.value.endDate,
                sn: currentDeviceSn.value,
                projectId: customerDetail.value.projectId,
                customerId: customerDetail.value.customerId,
            }
            await Promise.all([
                getChargeTimeDistribution(params),
                getChargeStartSocDistribution(params),
                getChargeCapacityDistribution(params),
            ])
        }
        const totalCapacity = computed(() => {
            if (!projectData.value?.bmsSummaryInfo?.totalDevices) return 0
            if (!projectData.value?.ratedPower) return 0
            return new Decimal(
                projectData.value?.bmsSummaryInfo?.totalDevices
            ).mul(new Decimal(projectData.value?.ratedPower))
        })
        const currentDeviceName = computed(() => {
            let res = equipmentData.value.find((item) => {
                return item.sn == currentDeviceSn.value
            })
            if (res) return res.remarkName
            return ''
        })
        return {
            currentDeviceName,
            chargeAnalysisDateRange,
            handleChargeAnalysisDateChange,
            handleTabChangeTotalAnalysi,
            totalAnalysisName,
            distributionChartDataYears,
            distributionChartDataDur,
            statisticsActiveName,
            handleStatisticsActiveNameTabChange,
            projectChargeTotalDateType,
            onProjectChargeTotalDateChange,
            changeViewRunningType,
            onChangeViewRunning,
            getVehicleType,
            vehicleTypes,
            activeDistributionName,
            handleActiveDistributionTabChange,
            runningDataPageTotal,
            runningDataPageInfo,
            runningDataPageChange,
            handleRunningDataCurrentChange,
            runningTableData,
            runningDataTableColumn,
            runningDataRangeDate24,
            selectedChargeStatus,
            chargeStatusOptions,
            handleChangeChargeStatus,

            runningDataRangeDate,
            onSearchRunningTableData,
            formatterStatus,
            chargeTotalDateType,
            onChargeTotalDateChange,
            chargeTotalDateOptions,
            deviceStatusActiveName,
            handleDeviceStatusTabChange,
            projectData,
            activeData,
            equipmentData,
            currentDeviceSn,
            handleAnalysisTabChange,
            analysisActiveName,
            handleActiveHistoryTabChange,
            activeHistoryName,
            onSelectDevice,
            popoverRef,
            onClickOutside,
            realData,
            basicInfoData,
            activeName,
            handleTabChange,
            getCompanyInfo,
            // descriptData,
            stationInfo,
            customerDetail,
            dayjs,
            chargeStatisticsData,
            unitConversion,
            alternateUnits,
            activeDeviceInfo,
            cellData,
            goRouter,
            defaultImg,
            // new
            getState,
            changeTab,
            activeKey,

            // 收益
            // 异常
            getAlarmDataFlag,

            // 分析
            //

            //
            loading,
            //
            // 新日期选择框
            chargeDateSearchChange,
            zhCn,

            getSegmentTypeColor,
            moment,

            // 车辆新数据
            showBmsBox,
            rankList,
            chargeType,
            toggleRank,
            statusData,
            SOCData,
            OffDevice,
            handleRefresh,
            // editDeviceVisible,
            alarmData,
            profitUnit,
            // onUpdateDevice,
            selectSupplierInfoCar,
            chargeViewType,
            onChangeView,
            deviceType,
            types,
            deviceTypes,
            selectedFields,
            showFields,
            onTypeChange,
            onShowFieldChange,
            onShowDeviceLineChange,
            rangeDate,
            deviceRangeDate,
            cascaderStartDate,
            onDateChange,
            onChartsDateChange,
            tableLoading,
            pageInfo,
            pageTotal,
            otherTableData,
            otherTableColumn,
            showMore,
            loadMoreLoading,
            loadMore,
            t,
            isDark,
            deviceSearchKeyword,
            onSearchDevice,
            onOpenPopover,
            deviceLineDataFiled,
            selectDeviceLineDataFiled,
            locale,
            totalCapacity,
            editNameVisible,
            onEditName,
            onUpdateDevice,
            formatterDeviceName,
        }
    },
}
</script>

<style scoped lang="less">
.device_detail {
    padding-top: 88px;
    .class-bt {
        height: 32px;
        font-size: 14px;
        padding: 4px 10px;
        color: #141414;
        border: 1px solid transparent;

        i {
            font-size: 18px;
        }

        :deep(.icons) {
            width: 18px;
            height: 18px;
            margin-right: 3px;
        }

        :deep(.icon-svg-box) {
            margin-right: 5px;
        }

        // &:hover {
        //     background-color: var(--themeColor);
        //     color: #fff;
        //     :deep(.icons) {
        //         color: #fff;
        //     }
        // }
    }

    .border-1 {
        padding: 4px 15px;
        font-size: 14px;
        height: 32px;

        &:hover {
            color: var(--themeColor);
            border-color: var(--themeColor);
        }

        &:focus {
            color: var(--themeColor) !important;
            border-color: var(--themeColor) !important;
        }
    }

    .my-m-l-1 {
        margin-left: 4px;
    }

    .go-box {
        .bt-box-go {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            background-color: #fff;
            text-align: center;

            &:hover {
                background-color: var(--themeColor);
                color: #fff;
            }
        }
    }

    .blockTabsb {
        background-color: #fff;
        box-sizing: border-box;
        padding: 0 1rem;

        & :deep(.ant-tabs-tab-active) {
            color: var(--themeColor);
            // background: #fff;
        }

        & :deep(.ant-tabs-ink-bar) {
            width: 32px !important;
            height: 3px;
            background-color: var(--themeColor) !important;
            // left: 50%;
            margin-left: 16px;
        }

        :deep(.ant-tabs-bar) {
            border-bottom: 0 !important;
            margin-bottom: 16px;
        }

        :deep(.ant-tabs-nav) {
            width: 100%;
        }

        :deep(.ant-tabs-content) {
            flex: 1;
        }

        :deep(.ant-tabs-tab) {
            margin: 0px;
            padding: 6px;
            font-size: 14px;
            border-radius: 4px;

            // background: #f5f5f5;
            &:hover {
                color: var(--themeColor);
            }
        }
    }

    .translateTabs {
        padding: 0 !important;
    }

    .tab-content {
        border-radius: 4px;
        border: 1px solid var(--border);
        overflow: hidden;

        .translate-svg {
            position: relative;
            height: 100%;

            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 80%;
                width: 1px;
                background: var(--border);
            }
        }
    }

    .bg-title-t {
        background-color: var(--bg-f5);
        color: #222;
        font-size: 0.75rem;
        padding: 10px;
    }

    .origin {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: rgba(51, 190, 79, 1);
        vertical-align: middle;
        border-radius: 50%;
    }

    .bt-box {
        :deep(.translate_detail) {
            .bg-title-t {
                background-color: #fff !important;
            }
        }

        .bg-bt {
            // background: rgba(245, 247, 247, 1);
            border-radius: 8px;
        }
    }

    .raduis-box {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: #3ecdda;
    }

    :deep(.ant-select-focused) {
        .ant-select-selector {
            border-color: var(--themeColor) !important;
            box-shadow: none !important;
        }
    }

    .w40 {
        width: 160px;
        font-size: 14px;

        :deep(.ant-select) {
            font-size: 14px;
        }

        :deep(.ant-select-selector) {
            height: 32px;
            padding: 0 11px;

            .ant-select-selection-search-input {
                height: 30px;
            }

            .ant-select-selection-item {
                line-height: 30px;
                padding-right: 18px;
            }

            .ant-select-selection-placeholder {
                line-height: 30px;
            }
        }

        :deep(.ant-select-arrow) {
            right: 11px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            font-size: 12px;
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    .range-picker {
        width: 240px;

        :deep(.ant-calendar-picker-input) {
            padding: 0px 11px;
            height: 32px;
            box-sizing: border-box;
            display: flex;

            .ant-calendar-range-picker-input {
                font-size: 14px;
                width: 90px;
                text-align: center;
            }

            .ant-calendar-picker-icon {
                right: 5px;
                // font-size: 14px;
                // top: 57%;
            }

            .ant-calendar-range-picker-separator {
                min-width: 30px;
                line-height: 30px;
                // vertical-align: middle !important;
            }
        }

        :deep(.ant-input) {
            &:focus {
                border-color: var(--themeColor);
                box-shadow: none;
            }
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    :deep(.ant-calendar-picker) {
        &:focus {
            .ant-calendar-picker-input {
                &:not(.ant-input-disabled) {
                    border-color: var(--themeColor) !important;
                    box-shadow: none;
                }
            }
        }
    }

    .my-rounded-lg {
        border-radius: 8px;
    }

    .icon-svg-box {
        width: 16px;
        height: 17.6px;
    }

    .flex-1-width {
        width: 618px;
    }
}

:deep(.topTabs .ant-tabs-nav) {
    display: none !important;
}

:deep(.ant-tabs-bar) {
    border: 0;
    margin-bottom: 0;
}

.device-tabs {
    padding: 16px 16px 0 16px;
}

:deep(.device-tabs .ant-tabs-nav) {
    display: inline-block !important;
    width: auto !important;
    background: var(--bg-f5);
    border-radius: 8px;
    padding: 4px;
}

//
.detail-info {
    // background: linear-gradient(180deg, #f6f6f6 0%, #ffffff 100%);
    background: var(--second-bg);
    border-radius: 8px;
    // backdrop-filter: blur(10px);
    border: 1px solid var(--border);
    border-radius: 8px;

    // border: 2px solid #ffffff;
    // background: #fff;
}

.alarm-item {
    height: 82px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    background: #f4f5f9;
    border-radius: 6px;
}

// .tabs-content {
//     position: relative;
//     margin-bottom: 18px;
//     transition: all 0.3s;
//     height: 100%;
//     .tabs-box {
//         height: 173px;
//         position: relative;
//     }
//     &::after {
//         display: none;
//         content: '';
//         width: 0;
//         height: 0;
//         border-top: 9px solid var(--second-bg);
//         border-left: 12px solid transparent;
//         border-right: 12px solid transparent;
//         border-bottom: 1px solid transparent;
//         position: absolute;
//         left: 50%;
//         margin-left: -6px;
//         margin-top: -1px;
//         top: 100%;
//     }

//     &.active {
//         .tabs-box {
//             box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);
//         }

//         &::after {
//             display: block;
//         }
//     }

//     &:hover {
//         .tabs-box {
//             box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);
//         }
//     }
// }
// .tabs-content {
//     border: 1px solid transparent;
//     position: relative;
//     .tabs-box {
//         height: 173px;
//     }
//     &::after {
//         display: block;
//         content: '';
//         width: 20px;
//         height: 11px;
//         // background: url(../../../assets/car/sj.png) no-repeat center center;
//         background-size: 100% 100%;
//         position: absolute;
//         left: 50%;
//         margin-left: -10px;
//         top: 100%;
//     }
//     &:hover {
//         border-color: var(--themeColor);
//         &::after {
//             // height: 11px;
//             // background: url(../../../assets/car/sj1.png) no-repeat center center;
//             // background-size: 100% 100%;
//         }
//     }
//     &.active {
//         border-color: var(--themeColor);
//         &::after {
//             height: 11px;
//             background: url(../../../assets/car/sj1.png) no-repeat center center;
//             background-size: 100% 100%;
//         }
//     }
// }
// .dark {
//     .tabs-content {
//         &.active {
//             &::after {
//                 height: 11px;
//                 background: url(../../../assets/car/sj2.png) no-repeat center
//                     center;
//                 background-size: 100% 100%;
//             }
//         }
//         // &.active {
//         //     filter: drop-shadow(0px 0px 1px rgb(111, 231, 255));
//         //     .tabs-box {
//         //         // box-shadow: 0px 0px 5px 2px rgba(62, 218, 205, 0.2);
//         //         z-index: 2;
//         //     }
//         //     &::after {
//         //         display: block;
//         //         z-index: 1;
//         //     }
//         // }
//         // &:hover {
//         //     filter: drop-shadow(0px 0px 1px rgb(111, 231, 255));
//         // }
//     }
// }

:deep(.ant-switch) {
    min-width: 40px;
    height: 24px;
}

.ant-switch-loading-icon,
.ant-switch::after {
    width: 20px;
    height: 20px;
}

:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
}
.dark .device_detail .blockTabsb {
    background-color: transparent;
}
.tabs {
    position: relative;
    &::before {
        display: block;
        content: '';
        border-bottom: 4px solid rgba(34, 34, 34, 0.04);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        position: absolute;
        left: 278px;
        bottom: 100%;
    }
}

:deep(.el-tabs__nav) {
    padding: 0;
    // background: var(--bg-f5);
    border-radius: 4px;
    border: 1px solid transparent;
    column-gap: 16px;
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}

:deep(.el-tabs__item) {
    // color: var(--text-100) !important;
    // font-weight: normal;
    // line-height: 36px;
    // height: 36px;
    // padding: 0 !important;
}

:deep(.el-tabs__item.is-active) {
    color: var(--themeColor) !important;
}

:deep(.el-tabs__item.is-active) {
    // background: #fff;
    // padding: 0 20px;
    border-radius: 4px !important;
    text-align: center;
    font-weight: 500;

    &::after {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -18px;
        width: 36px;
        height: 4px;
        background: var(--themeColor);
    }
}

:deep(.el-tabs--bottom) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}

:deep(.el-tabs--top) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}

:deep(.el-tabs__active-bar) {
    display: none;
}

.device-box {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    // border-radius: 4px;
    border-top: 2px solid var(--border);
    // background: #fff;
    padding: 14px 16px;
    margin-bottom: 12px;
}

.device-img {
    width: 46.76%;
    height: 370px;
}

.device-info {
    width: 53%;

    .content-desc {
        height: 325px;
    }

    .cell {
        height: 325px;
    }
}

:deep(.el-tabs__header) {
    margin-bottom: 0;
}

.toogle-icon {
    color: var(--themeColor);
}

:deep(.tags-tabs .el-tabs__item.is-active::after) {
    display: none !important;
}
:deep(.tags-tabs.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border-bottom-color: transparent !important;
}
:deep(.tags-tabs .el-tabs__item.is-active) {
    border-radius: 0 !important;
}
.car-item {
    &:hover {
        color: var(--themeColor);
    }
    &.active {
        color: var(--themeColor);
    }
}
</style>

<style lang="less">
.ant-calendar-today .ant-calendar-date {
    color: var(--themeColor);
    border-color: var(--themeColor);
}

.ant-calendar-selected-day .ant-calendar-date {
    background: var(--themeColor);
    border: 1px solid transparent;
    color: #fff;
}

.ant-calendar-date:active {
    background: var(--themeColor);
    color: #fff;
}

.ant-calendar-selected-date .ant-calendar-date {
    color: #fff;
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-in-range-cell::before {
    background: #f5f5f5;
}

.ant-calendar-picker .ant-calendar-picker-icon {
    margin-top: -8px;
}

.ant-calendar-picker .ant-calendar-picker-clear {
    display: none;
}

@keyframes changeWidth {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(119, 155, 219, 0.1);
        border-left: 43px solid transparent;
    }
}
.el-select-dropdown__item {
    color: var(--text-100);
}
.el-select-dropdown__item.is-selected {
    color: var(--themeColor);
    background-color: var(--selected-color);
}
.el-select-dropdown__item.is-hovering {
    color: var(--themeColor);
    background-color: var(--selected-color);
}
.el-popper.is-light {
    background: #fff;
}
.dark {
    .el-popper {
        border-color: transparent;
    }
    .el-popper.is-light {
        background: var(--main-bg);
    }
}
.box-border {
    border: 1px solid var(--border);
}
.search-group {
    :deep(.el-input) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}
</style>
