<template>
    <div class="operation">
        <div class="flex mb-4 justify-between">
            <div class="text-base leading-8 go-box flex items-center">
                <span
                    class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                    @click="goRouter"
                    >{{ $t('Home') }}</span
                >
                <iconSvg
                    name="rightIcon"
                    class="more-icon text-primary-text dark:text-80-dark"
                />
                <!-- 站点详情 -->
                <span class="ml-1">{{ $t('Power battery management') }}</span>
            </div>
        </div>
        <div class="operation-content relative">
            <div class="">
                <div
                    class="inline-flex items-center gap-x-8 menu relative z-1000"
                >
                    <div
                        class="menu-item"
                        :class="
                            currentMenuKey == 'operationCarProject'
                                ? 'active'
                                : ''
                        "
                        @click="goMenu('operationCarProject')"
                    >
                        {{ $t('Project management') }}
                    </div>
                    <div
                        class="menu-item"
                        :class="
                            currentMenuKey == 'operationCarDevice'
                                ? 'active'
                                : ''
                        "
                        @click="goMenu('operationCarDevice')"
                    >
                        {{ $t('Device Management') }}
                    </div>
                    <div
                        class="menu-item"
                        :class="
                            currentMenuKey == 'operationCarAlarm'
                                ? 'active'
                                : ''
                        "
                        @click="goMenu('operationCarAlarm')"
                    >
                        {{ $t('Alarm Management') }}
                    </div>
                </div>
            </div>
            <div class="view-content">
                <router-view> </router-view>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
const router = useRouter()
const store = useStore()

const goRouter = () => {
    router.push({
        path: '/vehicle',
    })
}

const route = useRoute()
const currentMenuKey = computed(() => {
    return route.name
})
const goMenu = (name) => {
    router.push({
        name: name,
    })
}

onMounted(async () => {})
</script>

<style lang="less" scoped>
.operation {
    padding-top: 88px;
}
.operation-content {
    background: var(--input-bg);
    border-radius: 8px;
    padding: 16px;
    padding-top: 22px;
    min-height: calc(~'100vh - 160px');
}

:deep(.el-tabs__header) {
    // margin-bottom: 30px;
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
    color: var(--text-60);
}
:deep(.el-tabs__item) {
    color: var(--text-100) !important;
}
:deep(.el-tabs__item.is-active) {
    color: var(--themeColor) !important;
}
:deep(.el-tabs__item:hover) {
    color: var(--themeColor) !important;
}
.menu {
    color: var(--input-color);
    .menu-item {
        padding-bottom: 8px;
        // border-bottom: 4px solid transparent;
        user-select: none;
        cursor: pointer;
        position: relative;
        &:after {
            display: block;
            content: '';
            width: 36px;
            height: 4px;
            background: transparent;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            transition: width 0.5s ease;
        }
        &:hover {
            color: var(--themeColor);
        }
        &.active {
            color: var(--themeColor);
            // border-color: var(--themeColor);
            &:after {
                // width: 100%;
                background-color: var(--themeColor);
            }
        }
    }
}
// .view-content {
//     height: calc(100vh - 200px);
//     position: relative;
// }
</style>
