<template>
    <div class="device_box">
        <div class="flex justify-between mb-3 items-center">
            <div
                class="cursor-pointer commenpy-title flex items-center"
                @click="visible = true"
            >
                <div
                    class="w-8 h-8 rounded bg-ff dark:bg-ff-dark flex justify-center items-center mr-1"
                    style="padding: 6px"
                >
                    <iconSvg
                        name="treeOpen"
                        class="w-5 h-5"
                        className="tree-svg-box"
                    />
                </div>
                <span>{{ orgNameValue }}</span>
            </div>
            <div class="flex gap-x-3">
                <!-- 只有拥有运维权限的人才可以看到该按钮 -->
                <el-button plain round @click="goOperation" v-if="isOperator">
                    <span>{{ $t('Management') }}</span>
                    <iconSvg
                        name="operation"
                        class="icon-default w-5 h-5 ml-0.5"
                    />
                </el-button>
                <el-button plain round @click="goMap">
                    <span>{{ $t('Map Perspective') }}</span>
                    <iconSvg name="ip" class="icon-default w-5 h-5 ml-0.5" />
                </el-button>

                <el-button plain round @click="goFull">
                    <span>{{ $t('Site Dashboard') }}</span>
                    <iconSvg
                        name="fullicon"
                        class="icon-default w-5 h-5 ml-0.5"
                    />
                </el-button>
            </div>
        </div>
        <div class="" v-loading="loading">
            <div class="flex gap-x-3 items-start justify-between">
                <!-- 站点 -->
                <div
                    class="tabs-content flex justify-end items-center rounded-lg bg-ff dark:bg-ff-dark cursor-pointer"
                    :class="activeKey === '1' ? 'active' : ''"
                    @click="debouncedChangeTab('1')"
                    style="width: 35%"
                >
                    <div class="w-1/3 h-full statistics-bg relative"></div>
                    <div
                        class="pl-0 pr-4 pt-3 pb-3 relative z-10"
                        style="height: 167px; width: 64.5%"
                    >
                        <div
                            class="flex items-end orgName rounded px-3 py-2"
                            style="background: rgba(149, 158, 195, 0.1)"
                        >
                            <div class="leading-6 text-base font-medium">
                                {{ $t('home_zhandianzongshu') }}
                            </div>
                            <div class="text-3.5xl leading-8 font-bold ml-3">
                                {{ onesPieceData?.totalStationQuantity || 0 }}
                            </div>
                            <div>{{ $t('tai') }}</div>
                        </div>
                        <div
                            class="flex justify-between items-center pl-3 mt-2"
                        >
                            <div class="">
                                <div class="mb-3">
                                    <span class="text-60"
                                        >{{ $t('home_fugaichengshi') }}：</span
                                    >
                                    <span
                                        class="text-base leading-none font-medium text-100"
                                    >
                                        {{ onesPieceData?.cityNum || 0 }}</span
                                    ><span
                                        class="ml-0.5 font-medium text-100"
                                        >{{ $t('common_ge') }}</span
                                    >
                                </div>
                                <div class="dark:text-80-dark">
                                    <span class="text-60"
                                        >{{ $t('Device capacity') }}：</span
                                    >
                                    <span
                                        class="text-base leading-none font-medium text-100"
                                    >
                                        {{
                                            unitConversion(
                                                onesPieceData.installedCapacity,
                                                1000
                                            )
                                        }} </span
                                    ><span class="ml-0.5 font-medium text-100"
                                        >({{
                                            alternateUnits(
                                                onesPieceData.installedCapacity ||
                                                    0,
                                                1000
                                            )
                                                ? 'MWh'
                                                : 'kWh'
                                        }})</span
                                    >
                                </div>
                            </div>
                            <div>
                                <div
                                    class="device-num-tag online items-center mb-2"
                                >
                                    <span class="device-num-tag-name">{{
                                        $t('Onlined')
                                    }}</span>
                                    <div class="device-num-tag-data">
                                        <span
                                            class="font-medium inline-block text-right num"
                                            style="min-width: 17px"
                                            >{{
                                                onesPieceData.onlineStationQuantity ||
                                                0
                                            }}</span
                                        ><span class="unit">{{
                                            $t('tai')
                                        }}</span>
                                    </div>
                                </div>
                                <div
                                    class="device-num-tag offline items-center"
                                >
                                    <span class="device-num-tag-name">{{
                                        $t('status_lixian')
                                    }}</span>
                                    <div class="device-num-tag-data">
                                        <span
                                            class="font-medium inline-block text-right num"
                                            style="min-width: 17px"
                                            >{{
                                                onesPieceData.offlineStationQuantity ||
                                                0
                                            }}</span
                                        ><span class="unit">{{
                                            $t('tai')
                                        }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 充放电 -->
                <div
                    class="tabs-content rounded-lg"
                    :class="activeKey === '2' ? 'active' : ''"
                    style="width: 44.375%"
                    @click="debouncedChangeTab('2')"
                >
                    <div class="p-3 bg-ff rounded-lg cursor-pointer">
                        <div class="">
                            <div class="charge-title flex justify-between">
                                <div
                                    class="charge-title-l h-12 pl-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end"
                                >
                                    <div class="leading-6 text-base">
                                        {{ $t('station_zuorichongdianliang') }}
                                    </div>
                                    <div
                                        class="text-3.5xl leading-8 font-bold"
                                        :class="
                                            locale == 'en' ? 'ml-2' : 'ml-3'
                                        "
                                    >
                                        {{
                                            unitConversion(
                                                pieceOthenData.yesterdayCharge,
                                                1000
                                            )
                                        }}
                                    </div>
                                    <div class="text-xs leading-5 ml-0.5">
                                        {{
                                            alternateUnits(
                                                pieceOthenData.yesterdayCharge,
                                                1000
                                            )
                                                ? 'MWh'
                                                : 'kWh'
                                        }}
                                    </div>
                                </div>
                                <div
                                    class="charge-title-r h-12 pr-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end justify-end"
                                >
                                    <div class="leading-6 text-base">
                                        {{ $t('station_zuorifangdianliang') }}
                                    </div>
                                    <div
                                        class="text-3.5xl leading-8 font-bold"
                                        :class="
                                            locale == 'en' ? 'ml-2' : 'ml-3'
                                        "
                                    >
                                        {{
                                            unitConversion(
                                                pieceOthenData.yesterdayDischarge,
                                                1000
                                            )
                                        }}
                                    </div>
                                    <div class="text-xs leading-5 ml-0.5">
                                        {{
                                            alternateUnits(
                                                pieceOthenData.yesterdayDischarge,
                                                1000
                                            )
                                                ? 'MWh'
                                                : 'kWh'
                                        }}
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex justify-between items-center px-3"
                                style="line-height: 42px"
                            >
                                <div class="flex-1 text-left">
                                    <span class="text-secondar-text">
                                        {{
                                            pieceOthenData?.beforeYesterdayCharge ==
                                            0
                                                ? $t('Previous day: No data')
                                                : $t('station_jiaoqianyiri') +
                                                  '：'
                                        }}
                                    </span>
                                    <percentage
                                        :num="
                                            pieceOthenData.comparedChargePercent
                                        "
                                    />
                                </div>
                                <div class="flex-1 text-right">
                                    <span class="text-secondar-text">
                                        {{
                                            pieceOthenData?.beforeYesterdayDischarge ==
                                            0
                                                ? $t('Previous day: No data')
                                                : $t('station_jiaoqianyiri') +
                                                  '：'
                                        }}
                                    </span>
                                    <percentage
                                        :num="
                                            pieceOthenData.comparedDischargePercent
                                        "
                                    />
                                </div>
                            </div>
                            <a-divider class="m-0" />
                            <div
                                class="flex justify-between items-center px-3 mt-3 leading-4"
                            >
                                <div class="flex-1 text-left flex">
                                    <div>
                                        <div class="text-secondar-text mb-2">
                                            {{ $t('station_yueleiji') }}({{
                                                alternateUnits(
                                                    pieceOthenData.currentMonthCharge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }})
                                        </div>
                                        <div
                                            class="font-medium text-base leading-4"
                                        >
                                            {{
                                                unitConversion(
                                                    pieceOthenData.currentMonthCharge,
                                                    1000
                                                )
                                            }}
                                        </div>
                                    </div>
                                    <div class="ml-6">
                                        <div class="text-secondar-text mb-2">
                                            {{ $t('station_zongji') }}({{
                                                alternateUnits(
                                                    pieceOthenData.totalCharge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }})
                                        </div>
                                        <div
                                            class="font-medium text-base leading-4"
                                        >
                                            {{
                                                unitConversion(
                                                    pieceOthenData.totalCharge,
                                                    1000
                                                )
                                            }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-1 text-right flex justify-end">
                                    <div>
                                        <div class="text-secondar-text mb-2">
                                            {{ $t('station_yueleiji') }}({{
                                                alternateUnits(
                                                    pieceOthenData.currentMonthDischarge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }})
                                        </div>
                                        <div
                                            class="font-medium text-base leading-4"
                                        >
                                            {{
                                                unitConversion(
                                                    pieceOthenData.currentMonthDischarge,
                                                    1000
                                                )
                                            }}
                                        </div>
                                    </div>
                                    <div class="ml-6">
                                        <div class="text-secondar-text mb-2">
                                            {{ $t('station_zongji') }}({{
                                                alternateUnits(
                                                    pieceOthenData.totalDischarge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }})
                                        </div>
                                        <div
                                            class="font-medium text-base leading-4"
                                        >
                                            {{
                                                unitConversion(
                                                    pieceOthenData.totalDischarge,
                                                    1000
                                                )
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 收益 -->
                <div
                    class="tabs-content rounded-lg"
                    :class="activeKey === '3' ? 'active' : ''"
                    style="width: 18.89%"
                    @click="debouncedChangeTab('3')"
                >
                    <div class="p-3 bg-ff rounded-lg cursor-pointer">
                        <div
                            class="h-12 leading-12 rounded text-sm font-medium"
                        >
                            {{ $t('station_zuorishouyi')
                            }}<span class="text-2.5xl font-bold ml-1">
                                {{
                                    formatterPrice(
                                        pieceOthenData.yesterdayProfit
                                    ).num
                                }}</span
                            >
                            <span class="text-sm">
                                {{
                                    formatterPrice(
                                        pieceOthenData.yesterdayProfit
                                    ).unit
                                }}
                            </span>
                        </div>
                        <div class="flex-1" style="line-height: 42px">
                            <span class="text-secondar-text">
                                {{
                                    pieceOthenData?.beforeYesterdayProfit == 0
                                        ? $t('Previous day: No data')
                                        : $t('station_jiaoqianyiri') + '：'
                                }}
                            </span>
                            <percentage
                                :num="pieceOthenData.comparedProfitPercent"
                            />
                        </div>
                        <a-divider class="m-0" />
                        <div class="flex justify-between mt-3 leading-4">
                            <div>
                                <div class="text-secondar-text mb-2">
                                    {{ $t('station_yueleiji')
                                    }}<template v-if="locale == 'zh'"
                                        >({{
                                            alternateUnits(
                                                pieceOthenData.currentMonthProfit,
                                                10000
                                            )
                                                ? '万元'
                                                : '元'
                                        }})
                                    </template>
                                    <template v-if="locale == 'en'">
                                        ({{
                                            alternateUnits(
                                                pieceOthenData.currentMonthProfit,
                                                1000
                                            )
                                                ? 'k'
                                                : ''
                                        }})
                                    </template>
                                </div>
                                <div class="font-medium text-base leading-4">
                                    {{
                                        locale == 'zh'
                                            ? unitConversion(
                                                  pieceOthenData.currentMonthProfit,
                                                  10000
                                              )
                                            : unitConversion(
                                                  pieceOthenData.currentMonthProfit,
                                                  1000
                                              )
                                    }}
                                </div>
                            </div>
                            <div>
                                <div class="text-secondar-text mb-2">
                                    {{ $t('station_zongji')
                                    }}<template v-if="locale == 'zh'"
                                        >({{
                                            alternateUnits(
                                                pieceOthenData.totalProfit,
                                                10000
                                            )
                                                ? '万元'
                                                : '元'
                                        }})</template
                                    >
                                    <template v-if="locale == 'en'">
                                        ({{
                                            alternateUnits(
                                                pieceOthenData.totalProfit,
                                                1000
                                            )
                                                ? 'k'
                                                : ''
                                        }})
                                    </template>
                                </div>
                                <div class="font-medium text-base leading-4">
                                    {{
                                        locale == 'zh'
                                            ? unitConversion(
                                                  pieceOthenData.totalProfit,
                                                  10000
                                              )
                                            : unitConversion(
                                                  pieceOthenData.totalProfit,
                                                  1000
                                              )
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <a-tabs
                class="w-full tabs pt-5 mt-0.5"
                :activeKey="activeKey"
                @change="tabchange"
            >
                <a-tab-pane key="1" tab="a">
                    <div class="h-full">
                        <!-- <el-tabs
                            v-model="activeName"
                            type="card"
                            class="demo-tabs"
                            @tab-change="handleTabChange"
                        > -->
                        <!-- 列表视图 -->
                        <!-- <el-tab-pane label="站点列表" name="list"> -->
                        <div class="flex justify-between items-center mb-3">
                            <div class="">{{ $t('home_zhandianliebiao') }}</div>
                            <div class="page-input">
                                <el-input
                                    v-model="tabSearchValue"
                                    :placeholder="$t('placeholder')"
                                    class="search-input"
                                    clearable
                                    @clear="onSearch"
                                >
                                    <template #append>
                                        <span
                                            class="inline-block px-3 cursor-pointer"
                                            @click="onSearch"
                                            ><iconSvg
                                                class="w-4 h-4 align-middle -mt-0.5"
                                                name="search"
                                        /></span>
                                    </template>
                                </el-input>
                            </div>
                        </div>
                        <div
                            class="box"
                            v-if="tableShow"
                            v-loading="deviceListLoading"
                        >
                            <div class="flex flex-wrap gap-3">
                                <template
                                    v-for="(item, index) in deviceList"
                                    :key="index"
                                >
                                    <device-box
                                        @click="goDetail(item)"
                                        :data="item"
                                        v-model:userType="userType"
                                    />
                                </template>
                            </div>
                            <div class="flex justify-center mt-10">
                                <el-pagination
                                    background
                                    layout="prev, pager, next"
                                    :total="pageConfig.total"
                                    v-model:current-page="pageConfig.current"
                                    :page-size="pageConfig.size"
                                    @change="debouncedChangePage"
                                />
                            </div>
                            <!-- <div class="flex justify-center mt-4">
                                        <a-pagination
                                            size="small"
                                            style="text-align: right"
                                            :current="pageConfig.current"
                                            :total="pageConfig.total"
                                            :pageSize="pageConfig.size"
                                            @change="changePage"
                                            @showSizeChange="changePageSize"
                                            :show-size-changer="false"
                                            show-quick-jumper
                                        />
                                    </div> -->
                        </div>
                        <!-- bg-ff rounded-lg -->
                        <div
                            class="empty flex justify-center items-center h-full"
                            v-else
                        >
                            <div class="img-box">
                                <empty-data :description="'暂未绑定站点'">
                                    <slot name="empty"></slot>
                                </empty-data>
                                <!-- <a-empty>
                                        <template #description>
                                            <span class="risk-empty-title"
                                                >暂未绑定站点</span
                                            >
                                        </template>
                                    </a-empty> -->
                            </div>
                            <el-button
                                plain
                                round
                                @click="siteVisible = true"
                                class="btn-hover"
                            >
                                <span>添加站点</span>
                                <span class="icon-box ml-0.5">
                                    <iconSvg
                                        name="addSn"
                                        class="icon-default"
                                    />
                                </span>
                            </el-button>
                        </div>
                        <!--  </el-tab-pane>
                          <el-tab-pane label="树状视图" name="tree">
                                <a-spin :spinning="treeLoading">
                                    <tree-diagram
                                        v-model:chartData="treeChartData"
                                        v-model:selectedKeys="selectedKeys"
                                        v-if="treeChartData?.id"
                                        @onExpand="onExpand"
                                    />
                                </a-spin>
                            </el-tab-pane>
                        </el-tabs> -->
                    </div>
                </a-tab-pane>
                <a-tab-pane key="2" tab="b">
                    <div
                        style="background-color: #fff"
                        class="p-4 h-full rounded-lg"
                    >
                        <div class="flex justify-between mb-2">
                            <span
                                style="color: #222222"
                                class="leading-loose"
                                >{{ $t('station_chongfangdiantongji') }}</span
                            >
                            <div class="flex justify-between gap-x-3">
                                <date-search
                                    :info="{
                                        periodOptions: ['day', 'month'],
                                        datePickerType: 'minute',
                                        dayRangeLen: 7,
                                    }"
                                    @onChange="dateSearchChange"
                                    v-model:dateSelect="dateSelect"
                                />

                                <export-button
                                    :content="
                                        $t('export_tips01')
                                            .replace(
                                                's%',
                                                dateSelect?.startDate ||
                                                    dateSelect?.startMonth
                                            )
                                            .replace(
                                                's%',
                                                dateSelect?.endDate ||
                                                    dateSelect?.endMonth
                                            )
                                    "
                                    @confirm="
                                        exportChart(chargeChartData, 'charge')
                                    "
                                />
                            </div>

                            <!-- <a-select
                                class="w-40"
                                placeholder="请选择"
                                :options="DateOptions"
                                v-model:value="chargeSelect"
                                @change="selectChange"
                            ></a-select> -->
                        </div>
                        <div>
                            <charge-charts
                                :chartData="chargeChartData"
                                :chargeSelect="chargeSelect"
                            />
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="3" tab="c">
                    <div class="h-full">
                        <div class="p-4 bg-ff rounded-lg">
                            <div class="flex justify-between mb-4">
                                <span
                                    style="color: #222222"
                                    class="leading-loose"
                                    >{{ $t('station_shouyitongji') }}</span
                                >
                                <div class="flex justify-between gap-x-3">
                                    <!-- <date-search
                                        :info="{
                                            periodOptions: ['day', 'month'],
                                            datePickerType: 'minute',
                                            dayRangeLen: 7,
                                        }"
                                        @onChange="incomeDateSearchChange"
                                        v-model:dateSelect="incomeDateSelect"
                                    /> -->
                                    <el-select
                                        v-model="projectChargeTotalDateType"
                                        style="width: 120px; border-radius: 8px"
                                        @change="onProjectChargeTotalDateChange"
                                    >
                                        <el-option
                                            v-for="item in chargeTotalDateOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>

                                    <export-button
                                        :content="
                                            $t('export_tips02').replace(
                                                's%',
                                                projectChargeTotalDateTypeLable
                                            )
                                        "
                                        @confirm="
                                            exportChart(
                                                incomeChartData,
                                                'profit'
                                            )
                                        "
                                    />
                                </div>
                            </div>
                            <div class="flex gap-x-5">
                                <div class="flex-1 w-0">
                                    <income-charts
                                        :chartData="incomeChartData"
                                        :incomeSelect="incomeSelect"
                                        :xType="projectChargeTotalDateType"
                                    />
                                </div>
                                <rank
                                    :rankList="rankList"
                                    :profitUnit="profitUnit"
                                />
                            </div>
                        </div>

                        <div>
                            <div class="mt-3 p-4 bg-ff rounded-lg">
                                <income-rank
                                    :dataSource="rankList"
                                    :profitUnit="profitUnit"
                                    :socialContribution="socialContribution"
                                />
                            </div>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="4" tab="d">
                    <div class="p-4 rounded-lg bg-ff">
                        <!-- <alarm-overview
                            v-model:getAlarmDataFlag="getAlarmDataFlag"
                        /> -->
                        <WorkOrder
                            title="异常"
                            v-model:getAlarmDataFlag="getAlarmDataFlag"
                            :supplierId="selectedKeys[0]"
                        />
                    </div>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
    <el-drawer
        v-model="visible"
        direction="ltr"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text">
                    <span>{{ orgNameValue }}</span>
                </div>
                <div>
                    <el-button plain round @click="onClose">关闭</el-button>
                </div>
            </div>
        </template>

        <el-tree
            style="max-width: 600px"
            default-expand-all
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            @node-click="handleNodeClick"
        />
        <!-- <tree :data="treeData" /> -->
    </el-drawer>

    <edit-info
        v-model:visible="siteVisible"
        @onClose="siteVisible = false"
        :isAddNew="true"
    />
</template>
<script>
import {
    reactive,
    ref,
    computed,
    onMounted,
    onUnmounted,
    onBeforeMount,
    nextTick,
    onBeforeUnmount,
} from 'vue'
import { useRouter } from 'vue-router'
import {
    BoardMap,
    DateOptionsMap,
    unitConversion,
    alternateUnits,
    formatterPrice,
    filterDate,
    someMax,
    roundNumFun,
    formatterChartData,
} from './const'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import _debounce from 'lodash/debounce'
import { message } from 'ant-design-vue'
import Percentage from './components/percentage.vue'
import DeviceBox from './home/<USER>'
import ChargeCharts from './home/<USER>'
import IncomeRank from './home/<USER>'
import WorkOrder from '@/views/operation/components/workOrder.vue'
import IncomeCharts from './home/<USER>'
import editInfo from '@/views/device/home/<USER>'
// import TreeDiagram from '@/components/treeDiagram-01.vue'
import DateSearch from '@/components/dateSearch.vue'
import moment from 'moment'
import * as XLSX from 'xlsx' //引入 xlsx 库，将数据转换为 Excel 并下载
import { useI18n } from 'vue-i18n'
import rank from './home/<USER>'
// import Tree from './tree.vue'
export default {
    components: {
        Percentage,
        DeviceBox,
        ChargeCharts,
        IncomeCharts,
        IncomeRank,
        // AlarmOverview,
        editInfo,
        // TreeDiagram,
        DateSearch,
        WorkOrder,
        rank,
        // Tree,
    },
    setup() {
        const { t, locale } = useI18n()
        const siteVisible = ref(false)
        const siteRef = ref(null)
        const addLoading = ref(false)
        const store = useStore()
        const router = useRouter()
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        const defaultProps = {
            label: 'name',
            children: 'children',
        }
        const replaceFields = {
            title: 'name',
            children: 'children',
            key: 'id',
        }

        const orgNameValue = ref(void 0)

        if (getCompanyInfo.value?.orgName) {
            orgNameValue.value = store.state.device.selectSupplierInfo?.id
                ? store.state.device.selectSupplierInfo?.name
                : getCompanyInfo.value.orgName
        }

        const treeData = ref([])

        const selectedKeys = ref([])

        const orgId = ref(
            getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
        )
        selectedKeys.value = store.state.device.selectSupplierInfo?.id
            ? [store.state.device.selectSupplierInfo?.id]
            : [orgId.value]
        // 获取设备列表
        const getDeviceInfo = async (supplierId) => {}
        // 递归遍历树并更新叶节点
        const updateTreeWithDeviceInfo = async (tree) => {
            // 使用一个辅助函数来递归遍历树
            async function traverseAndFetchDevices(node) {
                if (!node.children || node.children.length === 0) {
                    // 如果是叶节点，调用接口获取设备信息
                    const devices = await getDeviceInfo(node.id)
                    // 将设备信息添加到叶节点的 children 中
                    node.children = devices
                } else {
                    // 递归处理子节点
                    for (const child of node.children) {
                        await traverseAndFetchDevices(child)
                    }
                }
            }

            // 对每个根节点调用递归函数
            for (const root of tree) {
                await traverseAndFetchDevices(root)
            }
        }

        const treeChartData = ref()
        const getChartData = async (tree) => {
            // tree
            const newArr = _cloneDeep(tree)
            updateTreeWithDeviceInfo(newArr).then(() => {
                treeLoading.value = false
            })
        }
        const treeLoading = ref(false)
        const getChartDataByTreeSelectedKey = async (selectKeys) => {
            treeLoading.value = true
            const nodeData = searchNode(
                treeData.value,
                selectKeys.length ? selectKeys[0] : '1726907974555729921'
            )
            if (!nodeData) return
            await getChartData([nodeData])
        }
        // 默认获取一次treechartdaa
        const tableShow = ref(false)
        const addDraggedProperty = (tree) => {
            function traverse(node) {
                // 新增属性
                // node.noDragging = true
                // 如果节点有子节点，递归遍历子节点
                if (node.children && node.children.length > 0) {
                    node.children.forEach(traverse)
                }
            }
            // 开始遍历树结构
            if (tree?.length) {
                // 开始遍历树结构
                tree.forEach(traverse)
                return tree
            }
        }

        //站点树
        const getTreeData = async () => {
            const {
                data: { data, code },
            } = await apiService.getDeviceTree()
            if (code === 0) {
                const tree = [
                    {
                        name: getCompanyInfo?.value?.orgName,
                        id: getCompanyInfo?.value?.orgId,
                        children: addDraggedProperty(data) || [],
                        // noDragging: true,
                    },
                ]
                //
                treeData.value = tree
                // 从树结构中获取当前节点以及下属数据
                getChartDataByTreeSelectedKey(selectedKeys.value)
            }
        }

        const tableLoading = ref(false)
        const deviceListLoading = ref(false)
        //
        const onesPieceData = reactive({
            alarmQuantity: 0,
            installedCapacity: 0,
            installedPower: 0,
            offlineStationQuantity: 0,
            onlineStationQuantity: 0,
            totalStationQuantity: 0,
        })

        const pieceOthenData = reactive({
            comparedCharge: 0,
            comparedDischarge: 0,
            comparedProfit: 0,
            currentMonthCharge: 0,
            currentMonthDischarge: 0,
            currentMonthProfit: 0,
            totalCharge: 0,
            totalDischarge: 0,
            totalProfit: 0,
            yesterdayCharge: 0,
            yesterdayDischarge: 0,
            yesterdayProfit: 0,
        })

        //充电统计选择
        const chargeSelect = ref()
        //收益选择
        const incomeSelect = ref()

        //search
        const tabSearchValue = ref(void 0)

        const deviceList = ref([])
        const activeKey = ref('1')
        const tabsList = ref(['1']) // 定义点击过的列表
        const getAlarmDataFlag = ref(false) // 定义tab4的状态
        const tabchange = (key) => {
            activeKey.value = key
        }
        //收益
        let incomeEcharts = null
        let myObserver = null

        const resizeEcharts = () => {
            incomeEcharts && incomeEcharts.resize()
        }

        //社会贡献度
        const socialContribution = reactive({
            equivalentTreesQuantity: 0,
            savingCarbonDioxideEmission: 0,
            savingCarbonEmission: 0,
        })

        const getSocialContribution = async () => {
            let params = {}
            if (projectChargeTotalDateType.value === '1') {
                params = {
                    periodType: 'day',
                    startDate: moment()
                        .subtract(6, 'days')
                        .format('YYYY-MM-DD'),
                    endDate: moment().format('YYYY-MM-DD'),
                    supplierId: selectedKeys.value[0],
                }
            } else if (projectChargeTotalDateType.value === '2') {
                params = {
                    periodType: 'month',
                    startMonth: moment()
                        .subtract(11, 'months')
                        .format('YYYY-MM'),
                    endMonth: moment().format('YYYY-MM'),
                    supplierId: selectedKeys.value[0],
                }
            }
            const {
                data: { data, code },
            } = await apiService.getSocialContribution({
                ...params,
            })
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    socialContribution[key] = data[key]
                })
            } else {
                Object.keys(socialContribution).forEach((key) => {
                    socialContribution[key] = 0
                })
            }
        }

        const visible = ref(false)

        const onClose = () => {
            visible.value = false
        }

        const getSupplierStartOneData = async () => {
            try {
                const {
                    data: { data, code },
                } = await apiService.getSupplierStationSummaryOne({
                    supplierId: selectedKeys.value[0],
                })
                if (code === 0) {
                    Object.keys(data).forEach((key) => {
                        onesPieceData[key] = data[key] || 0
                    })
                } else {
                    Object.keys(onesPieceData).forEach((key) => {
                        onesPieceData[key] = 0
                    })
                }
            } catch (error) {
                //
            }
        }

        const getStatisticStationSummaryData = async () => {
            try {
                const {
                    data: { data, code },
                } = await apiService.getStatisticStationSummary({
                    supplierId: selectedKeys.value[0],
                    stationType: 'energy_storage_cabinet',
                })
                if (code === 0) {
                    Object.keys(data).forEach((key) => {
                        pieceOthenData[key] = data[key] || 0
                    })
                } else {
                    Object.keys(pieceOthenData).forEach((key) => {
                        pieceOthenData[key] = 0
                    })
                }
            } catch (error) {
                //
            }
        }
        // 获取收告警统计
        const alarmData = ref({
            totalQuantity: undefined,
            processingQuantity: undefined,
            todayQuantity: undefined,
            sevenDayQuantity: undefined,
        })
        const getStatisticalCard = async () => {
            let res = await apiService.getWorkOrderStatisticalCard({
                supplierId: selectedKeys.value[0],
                stationType: 'energy_storage_cabinet',
            })
            alarmData.value = res.data.data
        }

        const pageConfig = reactive({
            current: 1,
            size: 6,
            total: 0,
        })
        const setDeviceData = (arr) => {
            arr.forEach((item) => {
                if (item.stationNo == '')
                    if (item.children) {
                        //
                        setDeviceData(item.children)
                    } else {
                        item['total'] = item['installedCapacity']
                    }
            })
        }
        const mergeStationData = (multiLevelArray, flatArray) => {
            return multiLevelArray.map((item) => {
                if (item.stationNo) {
                    const match = flatArray.find(
                        (flatItem) => flatItem.stationNo === item.stationNo
                    )
                    if (match) {
                        item = { ...item, ...match } // 创建一个新对象
                    }
                }
                if (item.children && item.children.length > 0) {
                    item.children = mergeStationData(item.children, flatArray)
                }
                return item
            })
        }
        const setTopologyChartData = async (data) => {
            if (!data) return
            let newData = _cloneDeep(data)
            let res = await apiService.statisticOrgTotalCapacityAndProfit({
                orgId: newData.id,
            })
            newData['totalInstalledCapacity'] =
                res.data.data.totalInstalledCapacity
            newData['totalProfit'] = res.data.data.totalProfit
            // 获取服务商数据
            for (let item of newData.children) {
                if (item.resourceType == 'station') continue
                let res = await apiService.statisticOrgTotalCapacityAndProfit({
                    orgId: item.id,
                })
                item['totalInstalledCapacity'] =
                    res.data.data.totalInstalledCapacity
                item['totalProfit'] = res.data.data.totalProfit
            }

            // 获取服务商下一级站点数据
            const page = {
                current: 1,
                size: 100,
                supplierId: newData.id,
                includeSubOrg: true,
            }
            // 使用了获取所有站点的接口，后期替换成点击展开时获取下「一」级站点
            const res1 = await apiService.getPageList(page)
            if (res1.data.data.records.length) {
                // return arr
            }
            const result = mergeStationData(
                newData.children,
                res1.data.data.records
            )
            treeChartData.value = {
                ...newData,
                children: result,
            }
        }
        const hasRequest = ref([])
        const onExpand = async (e, data) => {
            if (hasRequest.value.includes(data.id)) return
            hasRequest.value.push(data.id)
            for (let item of data.children) {
                if (item.resourceType == 'station') continue
                let res = await apiService.statisticOrgTotalCapacityAndProfit({
                    orgId: item.id,
                })
                item['totalInstalledCapacity'] =
                    res.data.data.totalInstalledCapacity
                item['totalProfit'] = res.data.data.totalProfit
            }
        }
        const getOrgWithStationTopology = async () => {
            const res = await apiService.getOrgWithStationTopology({
                orgId: selectedKeys.value[0],
            })
            // setTopologyChartData(res.data.data)
        }
        const getTableList = async () => {
            try {
                const { current, size } = pageConfig
                const page = {
                    current,
                    size,
                    keyword: tabSearchValue.value,
                    supplierId: selectedKeys.value[0],
                }
                deviceListLoading.value = true
                const {
                    data: { data, code },
                } = await apiService.getPageList(page)
                if (code === 0) {
                    deviceList.value = data.records
                    pageConfig.total = data.total
                    pageConfig.current = data.current
                    tableShow.value = deviceList.value.length > 0 ? true : false
                }
                deviceListLoading.value = false
            } catch (error) {
                deviceListLoading.value = false
            }
        }
        const changePage = (e, size) => {
            pageConfig.current = e
            pageConfig.size = size
            getTableList()
        }
        const changePageSize = (e, size) => {
            pageConfig.current = e
            pageConfig.size = size
            getTableList()
        }

        // 防抖版本的分页函数
        const debouncedChangePage = _debounce((e, size) => {
            changePage(e, size)
        }, 300)

        const handleCurrentChange = (current) => {
            pageConfig.current = current
            getTableList()
        }

        const debouncedHandleCurrentChange = _debounce((current) => {
            handleCurrentChange(current)
        }, 300)
        //table搜索事件
        const onSearch = () => {
            pageConfig.current = 1
            getTableList()
        }

        const goMap = () => {
            const origin = window.location.origin
            window.open(
                `${origin}/#/device/siteOverview?supplierId=${selectedKeys.value[0]}`
            )
        }
        function searchNode(arr, id) {
            for (let item of arr) {
                if (item.id == id) {
                    return item
                } else if (item.children) {
                    let res = searchNode(item.children, id)
                    if (res) {
                        return res
                    }
                }
            }
            return null
        }

        const userType = computed(() => {
            // const node = searchNode(treeData.value, selectedKeys.value[0])
            // !node.node.children &&
            if (getCompanyInfo.value?.orgType == 'customer') {
                // 如果是客户视角
                return 'customer'
            } else if (getCompanyInfo.value?.orgType == 'supplier') {
                // 如果是供应商，但是没有子节点
                return 'supplier'
            } else {
                return 'all'
            }
        })
        const dateSelect = ref(null)
        const dateSearchChange = async (params) => {
            chargeSelect.value = dateSelect.value?.periodType
            await getChargeData(dateSelect.value)
        }
        const incomeDateSelect = ref(null)
        const handleNodeClick = (data) => {
            console.log('[ data ] >', data)
            const { id, name } = data
            orgNameValue.value = name
            selectedKeys.value = [id]
            visible.value = false
            store.commit('device/setSelectSupplierInfo', { id, name })
            getChartDataByTreeSelectedKey(selectedKeys.value)
            // 修改一下 切换过的tabs
            // tabsList.value = [activeKey.value]
            getSupplierStartOneData()
            getStatisticalCard()
            getStatisticStationSummaryData() //
            onSearch()
            if (activeName.value == 'tree') {
                getOrgWithStationTopology()
            }
            getAlarmDataFlag.value = false
            tabsList.value = []
            changeTab(activeKey.value, true)
        }

        const goFull = () => {
            // router.push({ path: '/fullScreen/device' })
            const origin = window.location.origin
            window.open(
                `${origin}/#/fullScreen/device?supplierId=${selectedKeys.value[0]}`
            )
        }
        const loading = ref(false)
        onBeforeMount(async () => {
            loading.value = true
            // 页面加载完成，调取接口
            await getTreeData() // 获取左侧树节点
            store.commit('device/setSelectSupplierInfo', {
                name: getCompanyInfo?.value?.orgName,
                id: getCompanyInfo?.value?.orgId,
            })
            // 页面加载时 先调接口
            await getSupplierStartOneData() //  获取服务商下的站点概要  顶部统计第一个站点统计
            await getStatisticStationSummaryData() // 获取站点充放电及收益摘要信息  顶部统计模块
            await getStatisticalCard() // 获取站点告警统计
            // getSocialContribution() // 统计社会贡献度  排碳量/二氧化碳等
            await getOrgWithStationTopology() // 获取站点拓扑
            pageConfig.current = store.state.device.pageCurrent || 1
            await getTableList() // 获取站点列表
            loading.value = false
        })
        onMounted(() => {
            localStorage.setItem('activeSystem', 'device')
            // observerResize()
            activeName.value = store.state.device.viewMode || 'list'
        })

        onUnmounted(() => {
            const someEl = document.querySelector(
                '#chartDeviceAnalysisEarnings'
            )
            someEl && myObserver.unobserve(someEl)
        })

        const onCloseSite = () => {
            siteRef.value.clearValidate()
            siteRef.value.resetFields()
            siteVisible.value = false
        }

        const bindStation = async (params) => {
            addLoading.value = true
            try {
                const {
                    data: { code, msg },
                } = await apiService.bindStation(params)
                if (code === 0) {
                    message.success('添加站点成功')
                    onCloseSite()
                    onSearch()
                }
                if (code != 0) {
                    message.error(msg)
                }
                addLoading.value = false
            } catch (error) {
                addLoading.value = false
            }
        }

        const submit = async () => {
            const value = await siteRef.value.submit()
            if (value) {
                const {
                    deviceSn,
                    stationName,
                    province,
                    city,
                    district,
                    address,
                    latitude,
                    longitude,
                    areaId,
                    electricType,
                    productModel,
                    containerQuantity,
                } = siteRef.value.formState
                const params = {
                    deviceSn,
                    stationName,
                    province,
                    city,
                    district,
                    address,
                    latitude,
                    longitude,
                    areaId,
                    electricType,
                    productModel,
                    containerQuantity,
                }
                bindStation(params)
            }
        }

        const chargeChartData = ref(null) //
        const rankList = ref(null) // 排序数据
        // 获取充放电统计数据
        const getChargeData = async (params) => {
            if (!params) {
                return
            }
            if (params?.periodType == 'hour') {
                const res = await apiService.statisticsStation24HourCharge({
                    ...params,
                    supplierId: selectedKeys.value[0],
                    stationType: 'energy_storage_cabinet',
                })
                chargeChartData.value = res.data.data
            } else {
                const res = await apiService.getElectricityAndRevenue({
                    ...params,
                    supplierId: selectedKeys.value[0],
                    stationType: 'energy_storage_cabinet',
                })
                chargeChartData.value = Object.values(res.data.data)
            }
        }

        //放电统计下拉selectChange
        const selectChange = () => {
            //
        }
        const incomeChartData = ref(null)
        //  获取收益数据
        const getIncomeData = async () => {
            let params = {}
            if (projectChargeTotalDateType.value === '1') {
                params = {
                    periodType: 'day',
                    startDate: moment()
                        .subtract(6, 'days')
                        .format('YYYY-MM-DD'),
                    endDate: moment().format('YYYY-MM-DD'),
                    supplierId: selectedKeys.value[0],
                }
            } else if (projectChargeTotalDateType.value === '2') {
                params = {
                    periodType: 'month',
                    startMonth: moment()
                        .subtract(11, 'months')
                        .format('YYYY-MM'),
                    endMonth: moment().format('YYYY-MM'),
                    supplierId: selectedKeys.value[0],
                }
            }
            const res = await apiService.getElectricityAndRevenue({
                ...params,
            })
            incomeChartData.value = res.data.data
        }
        // 获取收益排行
        const profitUnit = ref('元')
        const getRankData = async (data) => {
            let params = {
                periodType: 'day',
                startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
                endDate: moment().format('YYYY-MM-DD'),
                supplierId: selectedKeys.value[0],
                stationType: 'energy_storage_cabinet',
            }
            if (projectChargeTotalDateType.value === '1') {
                params.startDate = moment()
                    .subtract(6, 'days')
                    .format('YYYY-MM-DD')
            } else if (projectChargeTotalDateType.value === '2') {
                params.startDate = moment()
                    .subtract(364, 'days')
                    .format('YYYY-MM-DD')
            }
            const res = await apiService.getStatisticsStationProfitRank(params)
            rankList.value = res.data.data
            let max = 0
            rankList.value.forEach((item) => {
                if (+max < +item.profit) max = +item.profit
            })
            // 处理数据，计算百分比
            if (max) {
                rankList.value.forEach((item) => {
                    item['ratio'] = +(item.profit / max).toFixed(2)
                })
            }
            rankList.value.sort((a, b) => {
                return b.profit - a.profit
            })

            const arr = rankList.value.map((item) => {
                return item.profit
            })

            const isBooan = someMax(arr, 10000)
            profitUnit.value = isBooan && locale.value == 'zh' ? '万元' : '元'
            if (isBooan && locale.value == 'zh') {
                rankList.value?.forEach((item) => {
                    item.profit = item?.profit
                        ? roundNumFun(item.profit / 10000, 2)
                        : 0
                })
            }
        }

        // 切换选项卡
        const changeTab = async (key, flag) => {
            activeKey.value = key
            if (tabsList.value.includes(key) && !flag) {
                // 如果已经加载过,且没有执行onselect，则不调取接口
                return
            } else {
                tabsList.value.push(key)
                if (key === '2') {
                    // 充放电数据
                    nextTick(() => {
                        dateSearchChange()
                    })
                } else if (key == '3') {
                    // 收益数据
                    nextTick(() => {
                        getIncomeData()
                        getRankData()
                        getSocialContribution()
                    })
                } else if (key == '4') {
                    getAlarmDataFlag.value = false
                    //   由于这一模块是直接拿旧页面使用，所以给一个flg判断是否重新加载数据，onselect的时候需要重新加载，所以把这里改成true
                    // 延时设置，否则watch监听不到
                    setTimeout(() => {
                        getAlarmDataFlag.value = true
                    }, 100)
                }
            }
        }

        // 防抖版本的 tab 切换函数
        const debouncedChangeTab = _debounce((key, flag) => {
            changeTab(key, flag)
        }, 300)
        // 跳到详情
        const goDetail = (record) => {
            const { stationNo } = record
            // setPageCurrent设置vuex中的值
            store.commit('device/setPageCurrent', pageConfig.current)
            sessionStorage.setItem(
                'stationPic',
                record.stationPic || require('@/assets/device/defaultImg.png')
            )
            router.push({
                name: 'deviceDetail',
                query: { stationNo, supplierId: selectedKeys.value[0] },
                params: { stationPic: record.stationPic },
            })
        }
        //
        const activeName = ref()
        const handleTabChange = (e) => {
            //
            window.scrollTo(0, 300)
            store.commit('device/setViewMode', e)
        }
        const exportChart = (val, type) => {
            // 导出excel
            let data
            if (type == 'charge') {
                data = val.map((obj) => ({
                    [t('Time')]: obj?.date || '',
                    [t('Charging amount') + '(kWh)']: obj?.charge || 0,
                    [t('Discharging amount') + '(kWh)']: obj?.discharge || 0,
                    // '充放电收益(元)': obj?.profit || 0,
                }))
            } else {
                data = val.map((obj) => ({
                    [t('Time')]: obj?.date || '',
                    // '充电量(kWh)': obj?.charge || 0,
                    // '放电量(kWh)': obj?.discharge || 0,
                    [t('station_chongfangdianshouyi') + '(元)']:
                        obj?.profit || 0,
                }))
            }

            // 将数据转换为 worksheet 对象
            const worksheet = XLSX.utils.json_to_sheet(data)
            // 将 worksheet 对象添加到 workbook 中
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
            // Excel 文件
            XLSX.writeFile(
                workbook,
                type == 'charge'
                    ? t('station_chongfangdiantongji') + '.xlsx'
                    : t('station_chongfangdianshouyi') + '.xlsx'
            )
        }
        // 去运维
        const goOperation = () => {
            router.push({
                name: 'operation',
            })
        }
        const isOperator = computed(() => {
            return store.state.user.userInfoData.roles.includes(
                'operation_staff'
            )
            // return false
        })
        const selectedValue = ref('stationName')
        // 收益统计模块改版
        const statisticsActiveName = ref('b')
        const projectChargeTotalDateType = ref('1')

        const chargeTotalDateOptions = ref([
            {
                label: t('Last Week'),
                value: '1',
            },
            {
                label: t('Last Year'),
                value: '2',
            },
        ])
        const projectChargeTotalDateTypeLable = computed(() => {
            return chargeTotalDateOptions.value.find(
                (item) => item.value == projectChargeTotalDateType.value
            ).label
        })
        const onProjectChargeTotalDateChange = async () => {
            getIncomeData()
            getRankData()
        }
        return {
            formatterPrice,
            locale,
            projectChargeTotalDateTypeLable,
            onProjectChargeTotalDateChange,
            statisticsActiveName,
            projectChargeTotalDateType,
            chargeTotalDateOptions,
            BoardMap,
            DateOptions: Object.entries(DateOptionsMap).map(([value, key]) => ({
                key,
                value,
                label: key,
            })),
            visible,
            treeData,
            onClose,
            onesPieceData,
            pieceOthenData,
            unitConversion,
            alternateUnits,
            chargeSelect,
            incomeSelect,
            selectChange,
            socialContribution,
            pageConfig,
            onSearch,
            tabSearchValue,
            tableLoading,
            deviceList,
            getCompanyInfo,
            dayjs,
            goMap,
            replaceFields,
            selectedKeys,
            tabchange,
            activeKey,
            orgNameValue,
            goFull,
            tableShow,
            // expand,
            siteVisible,
            onCloseSite,
            submit,
            siteRef,
            addLoading,
            //
            changeTab,
            debouncedChangeTab,
            tabsList,
            goDetail,
            chargeChartData,
            incomeChartData,
            rankList,
            getAlarmDataFlag,
            profitUnit,
            //
            alarmData,
            userType,
            treeChartData,
            activeName,
            handleTabChange,
            treeLoading,
            dateSelect,
            dateSearchChange,
            incomeDateSelect,
            onExpand,
            exportChart,
            changePage,
            changePageSize,
            debouncedChangePage,
            handleCurrentChange,
            debouncedHandleCurrentChange,
            deviceListLoading,
            goOperation,
            isOperator,
            loading,
            selectedValue,
            // 新的树形结构
            defaultProps,
            handleNodeClick,
        }
    },
}
</script>
<style lang="less" scoped>
.device_box {
    min-height: calc(100vh - 128px);
    padding-top: 88px;
    .w40 {
        width: 160px;

        :deep(.ant-select) {
            font-size: 14px;
        }

        :deep(.ant-select-selector) {
            height: 32px;
            padding: 0 11px;

            .ant-select-selection-search-input {
                height: 30px;
            }

            .ant-select-selection-item {
                line-height: 30px;
                padding-right: 18px;
            }
        }

        :deep(.ant-select-arrow) {
            right: 11px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            font-size: 12px;
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    .e-height-bt {
        height: 415px;
    }

    .my-text-lg {
        font-size: 18px;
        line-height: 28px;
    }

    .span-width {
        width: 100px;

        .span-width {
            width: 100px;
        }
    }

    .empty {
        display: flex;
        justify-content: center;
        flex-direction: column;
        padding: 40px 0;

        .img-box {
            display: flex;
            justify-content: center;
        }

        .empty-title {
            margin-top: 27px;
            display: flex;
            justify-content: center;
            align-items: center;

            :deep(.sn-add) {
                cursor: pointer;
                width: 16px;
                height: 16px;
            }

            span {
                cursor: pointer;
                height: 22px;
                font-size: 14px;

                color: #222222;
                line-height: 22px;
            }

            &:hover {
                span {
                    color: var(--themeColor);
                }

                :deep(.sn-add) {
                    color: var(--themeColor);
                    fill: var(--themeColor);
                }

                :deep(.sn-add svg) {
                    color: var(--themeColor);
                    fill: var(--themeColor);
                }

                :deep(.sn-add path) {
                    color: var(--themeColor);
                    fill: var(--themeColor);
                }

                :deep(.sn-add g) {
                    color: var(--themeColor);
                    fill: var(--themeColor);
                }
            }
        }
    }

    :deep(.ant-tabs-bar) {
        border: 0;
        margin-bottom: 0px;
    }

    .commenpy-title {
        // font-size: 0.9rem;
        color: var(--text-80);

        font-size: 16px;
        line-height: 24px;

        span {
            img {
                vertical-align: sub;
                width: 16px;
            }
        }

        .btn {
            &:hover {
                background-color: var(--themeColor);
                color: #fff;

                :deep(.tree-svg-box) {
                    color: #fff;
                }
            }
        }
    }

    :deep(.ant-table-pagination) {
        margin-bottom: 0 !important;
    }

    :deep(.ant-table-thead > tr > th) {
        background: none;
        color: rgba(34, 34, 34, 0.6);
        padding: 12px 12px 12px 8px;
        font-size: 14px;
    }

    :deep(.ant-table-tbody > tr > td) {
        padding: 12px;
        font-size: 14px;

        color: #222222;
        padding: 12px 12px 12px 8px;
        cursor: pointer;
    }

    :deep(.ant-table-tbody) {
        > tr:hover:not(.ant-table-expanded-row) > td,
        .ant-table-row-hover,
        .ant-table-row-hover > td {
            background: #f5f5f5 !important;
        }
    }

    :deep(.ant-pagination) {
        display: flex;
        align-items: center;

        .ant-pagination-prev,
        .ant-pagination-next {
            line-height: 32px;
            height: 32px;
            min-width: 32px;

            .ant-pagination-item-link {
                font-size: 12px;
            }
        }

        .ant-pagination-item {
            min-width: 32px;
            height: 32px;
            margin-right: 8px;
            line-height: 30px;
            font-size: 14px;

            &:hover {
                a {
                    color: var(--themeColor);
                }
            }
        }

        .ant-pagination-item-active {
            border-color: var(--themeColor);

            a {
                color: var(--themeColor);
            }
        }

        .ant-pagination-options {
            margin-left: 16px;
            display: flex;
            align-items: center;

            .ant-select-selector {
                padding: 0 11px;
                height: 32px;
                font-size: 14px;

                .ant-select-selection-item {
                    padding-right: 18px;
                    height: 32px;
                    line-height: 30px;
                }
            }

            .ant-select {
                &:not(.ant-select-disabled) {
                    &:hover {
                        .ant-select-selector {
                            border-color: var(--themeColor);
                        }
                    }
                }

                .ant-select-arrow {
                    right: 11px;
                    width: 12px;
                    height: 12px;
                    margin-top: -6px;
                    font-size: 12px;
                }

                .ant-select-item {
                    padding: 5px 12px;
                    line-height: 22px;
                    font-size: 14px;
                    min-height: 32px;

                    .ant-select-item-option-content {
                        font-size: 14px;
                    }
                }
            }

            .ant-select-focused {
                .ant-select-selector {
                    border-color: var(--themeColor);
                    box-shadow: none !important;
                }
            }

            .ant-pagination-options-quick-jumper {
                height: 32px;
                line-height: 32px;
                font-size: 14px;

                input {
                    padding: 4px 11px;
                    width: 44px;
                    font-size: 14px;

                    &:hover {
                        border-color: var(--themeColor);
                    }

                    &:focus {
                        border-color: var(--themeColor);
                        box-shadow: none;
                    }
                }
            }
        }
    }

    .icon-svg-box {
        width: 28px;
        height: 28px;
    }
}

.barProgress {
    background-color: #faad14;
    height: 10px;
    display: inline-block;
}

.device-num-tag {
    // padding: 4px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 24px;
    align-items: center;
    padding: 0 15px;
    font-size: 0;
    height: 24px;
    display: flex;

    .device-num-tag-name {
        width: 29px;
        text-align: left;
        font-size: 14px;
    }
    .device-num-tag-data {
        width: 42px;
        text-align: right;
        display: flex;
        line-height: 24px;
        height: 24px;
        align-items: center;
        justify-content: flex-end;
    }
    span {
        font-size: 12px;
        // vertical-align: baseline;
    }
    .num {
        font-size: 16px;
        line-height: 1;
        // vertical-align: top;
    }
    .unit {
        line-height: 18px;
        padding-top: 1px;
    }

    // vertical-align: text-bottom;
}
.online {
    background: var(--tag-online-bg);
    color: var(--tag-online-color);
}

.offline {
    background: var(--tag-offline-bg);
    color: var(--tag-offline-color);
}
.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(241, 246, 255, 1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(241, 246, 255, 1);
        border-left: 43px solid transparent;
    }
}

:deep(.ant-tabs-nav) {
    display: none !important;
}

.tabs {
    min-height: calc(~'100vh - 328px');
    padding-bottom: 30px;
}

// .tabs-box {
//     position: relative;
//     margin-bottom: 12px;
//     transition: all 0.3s;

//     &::after {
//         display: none;
//         content: '';
//         width: 0;
//         height: 0;
//         border-top: 9px solid #fff;
//         border-left: 12px solid transparent;
//         border-right: 12px solid transparent;
//         border-bottom: 1px solid transparent;
//         position: absolute;
//         left: 50%;
//         margin-left: -6px;
//         margin-top: -1px;
//         top: 100%;
//     }

//     &.active {
//         box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);

//         &::after {
//             display: block;
//         }
//     }

//     &:hover {
//         box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);
//     }
// }

.device-box {
    width: calc(~'33.333% - 8px');
    // background: linear-gradient(180deg, #f6f6f6 0%, #ffffff 100%);
    background: #fff;
    border: 2px solid #ffffff;
}

:deep(.el-tabs--card > .el-tabs__header) {
    border: none;
    height: 60px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    // width: 104px; // 208px  xxxx
    // width: 208px; // xxxx
    background: rgba(34, 34, 34, 0.04);
    border: none;
    padding: 8px;
    border-radius: 4px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border: none;
    border-color: transparent;
    background: #fff;
    border-radius: 4px;
}

.page-input {
    position: relative;
    .search-input {
        width: 240px;
    }
    .input-with-select {
        position: relative;
    }

    :deep(.el-input-group__append) {
        padding: 0;
    }

    :deep(.el-input__suffix) {
        text-align: center;
    }

    // :deep(.el-input .el-input__icon) {
    //     margin: 0 4px;
    // }
}

// 树组件

:deep(.el-tree-node__content) {
    flex-direction: row-reverse;
    height: 40px;
    border-radius: 4px;
    .el-tree-node__label {
        line-height: 40px;
    }
    &:hover {
        // background: #f5f7f7;
        color: var(--themeColor);
        .el-tree-node__label {
            &::before {
                border-color: var(--themeColor);
            }
        }
        * {
            fill: var(--themeColor);
        }
        .el-tree-node__expand-icon::before {
            background: var(--themeColor);
        }
        .el-tree-node__expand-icon.expanded::before {
            background: var(--themeColor);
        }
    }
}
:deep(.el-tree-node__label) {
    flex: 1;
    position: relative;
    text-indent: 8px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-bottom-left-radius: 2px;
        border-left: 1px solid rgba(34, 34, 34, 0.08);
        border-bottom: 1px solid rgba(34, 34, 34, 0.08);
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
    }
}
:deep(.el-tree) {
    display: block;
}
.el-tree {
    display: block;
    > :deep(.el-tree-node) {
        > .el-tree-node__content {
            > .el-tree-node__label {
                display: block;

                &::before {
                    display: none;
                }
            }
        }
    }
}
:deep(
        .el-tree--highlight-current
            .el-tree-node.is-current
            > .el-tree-node__content
    ) {
    border-radius: 4px;
}
:deep(.el-tree-node) {
    .el-tree-node__expand-icon {
        font-size: 20px;
        color: #fff;
    }

    .el-tree-node__expand-icon::before {
        content: '+';
        font-size: 12px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.expanded::before {
        content: '-';
        font-size: 14px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.is-leaf {
        display: none;
    }
    .el-tree-node__expand-icon.el-icon-caret-right {
        transform: none !important;
    }
    .el-tree-node__expand-icon {
        font-style: normal !important;
        transform: none !important;
        svg {
            display: none !important;
        }
    }
}
</style>

<style lang="less">
.ant-modal-confirm-btns {
    float: none;
    text-align: center;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: #f5f5f5;
}

.ant-select-item {
    min-height: 32px;
    padding: 5px 12px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
}
</style>

// 树节点魔改
<style lang="less">
.tree-nodes {
    background: #f5f7f7;
    padding: 12px;
    border-radius: 4px;
}

.ant-tree li .ant-tree-node-content-wrapper {
    display: block;
    width: 100% !important;
    padding-right: 30px;
    height: 44px;
    line-height: 44px;
    padding: 0 12px;

    &:hover {
        background: var(--themeColor);
        color: #fff;
    }
}

.ant-tree li span.ant-tree-switcher {
    position: absolute;
    right: 10px;
    top: 12px;
}

ul.ant-tree > li > .ant-tree-node-content-wrapper {
    margin-bottom: 10px;
}

ul.ant-tree > li:last-child > .ant-tree-node-content-wrapper {
    margin-bottom: 0;
}

ul.ant-tree > li > ul {
    background: #fff;
    border-radius: 4px;
    margin-top: 10px;
}

.ant-tree li ul {
    width: calc(~'100% - 16px');
    margin-left: 8px;
    padding: 12px;
}

.ant-tree li {
    width: 100%;
    position: relative;
    padding: 0;
}

.ant-tree-child-tree > li:first-child {
    padding: 0;
}

.ant-tree li + li {
    margin-top: 10px;
}

ul.ant-tree > li > ul > li {
    background: #f5f7f7;
    border-radius: 4px;
}

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    border-radius: 4px;
}

.ant-tree > li:last-child {
    padding: 0;
}

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background: var(--themeColor);
    color: #fff;
}

.tree-nodes {
    .ant-tree-treenode-selected {
        > .ant-tree-switcher {
            svg {
                color: #fff !important;
            }
        }
    }
}

ul.ant-tree > li > ul > li > ul > li {
    background: #fff;
    border-radius: 4px;
}

ul.ant-tree > li > ul > li > ul > li > ul > li {
    background: #f5f7f7;
    border-radius: 4px;
}

ul.ant-tree > li > ul > li > ul > li > ul > li > ul > li {
    background: #fff;
    border-radius: 4px;
}

.zm-draggable {
    width: 100%;
}

.tree-org {
    width: 100%;
}

.ant-pagination-options {
    .ant-pagination-options-size-changer.ant-select {
        // width: 80px;
    }
}
.statistics-bg {
    // height: 166px;
    background: url(../../assets/car/statistics_bg.png) no-repeat center center;
    background-size: 100% 100%;
    width: 50%;
    position: absolute;
    left: 0;
    top: 0;
}
</style>
