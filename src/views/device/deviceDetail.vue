<template>
    <a-spin :spinning="loading">
        <div class="device_detail" ref="containerRef">
            <div class="flex mb-4 justify-between">
                <div
                    class="text-base leading-8 go-box flex items-center text-primary-text dark:text-80-dark"
                >
                    <span
                        class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                        @click="goRouter"
                        >{{ selectSupplierInfo.name || '-' }}</span
                    >

                    <iconSvg
                        name="rightIcon"
                        class="more-icon text-primary-text dark:text-80-dark"
                    />
                    <!-- 站点详情 -->
                    <span class="ml-1 text-primary-text dark:text-80-dark">{{
                        stationInfo.stationName
                    }}</span>
                </div>
                <div class="flex cursor-pointer space-x-2">
                    <el-button
                        plain
                        round
                        @click="goStrategy"
                        class="btn-hover flex items-center"
                        v-if="
                            (emsInfo?.emsType == 'mingWork' ||
                                emsInfo?.emsType == 'mingwork') &&
                            isEmsControl
                        "
                    >
                        <span>{{ $t('station_celueguanli') }}</span>
                        <span class="icon-box ml-0.5">
                            <iconSvg name="ems" class="icon-default" />
                        </span>
                    </el-button>
                    <a-dropdown
                        :trigger="['click']"
                        v-if="transformers.devices?.length > 1"
                    >
                        <el-button
                            plain
                            round
                            class="btn-hover flex items-center"
                            v-if="emsInfo?.emsType == 'jianJie' && isEmsControl"
                        >
                            <span>{{ $t('station_guanlihoutai') }}</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg name="ems" class="icon-default" />
                            </span>
                        </el-button>
                        <template #overlay>
                            <a-menu>
                                <a-menu-item
                                    v-for="(item, index) in containerList"
                                    :key="index"
                                >
                                    <div
                                        class=""
                                        @click="goBlack(item?.emsUrl)"
                                    >
                                        {{ $t('station_jigui') }}
                                        {{ item.containerNo.substring(3) }}
                                    </div>
                                </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>
                    <template v-else>
                        <el-button
                            plain
                            round
                            class="btn-hover flex items-center"
                            v-if="emsInfo?.emsType == 'jianJie' && isEmsControl"
                            @click="goBlack(containerList[0]?.emsUrl)"
                        >
                            <span>{{ $t('station_guanlihoutai') }}</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg name="ems" class="icon-default" />
                            </span>
                        </el-button>
                    </template>
                </div>
            </div>
            <div
                class="flex detail-info mb-3 p-4 items-center bg-ff dark:bg-car-pie-border"
            >
                <div class="flex-1 w-0">
                    <div class="flex gap-x-7 items-center">
                        <div
                            class="bg-ff dark:bg-ff-dark rounded overflow-hidden"
                            style="width: 333px; height: 250px"
                        >
                            <img
                                :src="stationInfo.stationPic"
                                class="w-full h-full"
                                alt=""
                                srcset=""
                            />
                        </div>

                        <div class="flex-1 pr-8 w-0">
                            <div class="flex justify-between items-center">
                                <div
                                    class="flex flex-1 items-center mb-1 gap-x-5"
                                >
                                    <div
                                        class="overflow text-2xl font-medium h-8 text-title dark:text-title-dark"
                                        v-if="stationInfo.stationName"
                                    >
                                        {{ stationInfo.stationName || '' }}
                                    </div>
                                    <div
                                        class="overflow text-2xl font-medium bg-ff dark:bg-ff-dark h-8 w-40"
                                        v-else
                                    ></div>
                                    <div
                                        class="h-6 w-6 cursor-pointer text-title dark:text-title-dark"
                                        @click="handleEditInfo"
                                    >
                                        <iconSvg name="edit" class="w-6 h-6" />
                                    </div>
                                </div>
                                <template v-if="isDemoUser && 1 > 2">
                                    <template v-if="!vppInfo.openVpp">
                                        <div
                                            class="text-center items-center rounded-full flex gap-x-2 text-ff px-3 h-8 select-none cursor-pointer"
                                            :class="
                                                stationInfo.status === 3
                                                    ? 'opacity-40 cursor-not-allowed'
                                                    : ''
                                            "
                                            style="background: #7d4af9"
                                            @click="openAiModal"
                                        >
                                            <img
                                                src="@/assets/device/ai.png"
                                                class="w-5 h-5"
                                                alt=""
                                                srcset=""
                                            />
                                            <div>启用AI模式</div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div
                                            class="text-center items-center rounded-full flex gap-x-2 text-ff px-3 h-8 select-none cursor-pointer"
                                            style="
                                                border: 1px solid #7d4af9;
                                                color: #7d4af9;
                                            "
                                            @click="AiOptimize"
                                        >
                                            <img
                                                src="@/assets/device/ai.png"
                                                class="w-5 h-5"
                                                alt=""
                                                srcset=""
                                            />
                                            <div>AI策略优化</div>
                                        </div>
                                        <ai-optimize-drawer
                                            v-model:visible="AiOptimizeVisible"
                                            @close="AiOptimizeClose"
                                            :data="{
                                                stationName:
                                                    stationInfo.stationName,
                                                stationNo:
                                                    stationInfo.stationNo,

                                                ...emsInfo,
                                                openVpp: vppInfo.openVpp,
                                                openAiStrategy:
                                                    vppInfo.openAiStrategy,
                                                openVppDemand:
                                                    vppInfo.openVppDemand,
                                            }"
                                        />
                                    </template>
                                </template>
                            </div>
                            <div class="flex items-center mb-4">
                                <div
                                    class="text-xs text-secondar-text dark:text-60-dark leading-6"
                                >
                                    {{ $t('No') }}：{{
                                        stationInfo.stationNo || '-'
                                    }}
                                </div>
                                <div
                                    class="flex items-center leading-6 gap-x-2 ml-2"
                                >
                                    <div
                                        class="flex items-center gap-x-1 px-2"
                                        style="
                                            color: var(--tag-orange-color);
                                            background: var(--tag-orange-bg);
                                        "
                                    >
                                        <!-- <iconSvg name="income" class="w-4 h-4" style="" /> -->
                                        <div class="text-xs leading-5.5">
                                            {{ $t('station_zongshouyi') }} :{{
                                                transformPrices(
                                                    pieceOthenData.totalProfit
                                                )
                                            }}
                                        </div>
                                    </div>
                                    <!-- 暂时注释 -->
                                    <!-- <div
                                        class="flex items-center gap-x-1 px-2"
                                        style="
                                            color: #52c41a;
                                            background: rgba(246, 255, 237, 1);
                                        "
                                    >
                                        <div class="text-xs leading-5.5">
                                            运行效率: {{ pieceOthenData.efficiency }}%
                                        </div>
                                    </div> -->
                                    <div
                                        v-if="stationInfo.status !== null"
                                        class="flex items-center gap-x-1"
                                        :style="{
                                            color: getState(stationInfo.status)
                                                .color,
                                        }"
                                    >
                                        <i
                                            class="w-6 h-6 text-2xl leading-6"
                                            :class="[
                                                'iconfont',
                                                getState(stationInfo.status)
                                                    .icon,
                                            ]"
                                        ></i>
                                        <span>{{
                                            // $t(getState(stationInfo.status).label)
                                            getState(stationInfo.status).label
                                                ? $t(
                                                      getState(
                                                          stationInfo.status
                                                      ).label
                                                  )
                                                : ''
                                        }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="h-6">
                                <template v-if="dayDiff <= 0 && 1 > 2">
                                    <div class="flex text-xs">
                                        <!-- <div class="text-ai">AI模式已启动</div> -->
                                        <div
                                            class="px-2 rounded-sm h-5 leading-5"
                                            style="
                                                background: rgba(
                                                    22,
                                                    119,
                                                    255,
                                                    0.1
                                                );
                                                color: #1677ff;
                                            "
                                        >
                                            智能均衡模式
                                        </div>
                                    </div>
                                </template>
                            </div>
                            <div
                                class="flex justify-between px-2 items-center mb-3"
                            >
                                <div class="flex-1">
                                    <div
                                        class="text-secondar-text dark:text-60-dark"
                                    >
                                        {{
                                            $t('station_zhuangjirongliang')
                                        }}(kWh)
                                    </div>
                                    <div
                                        class="text-base font-medium text-title dark:text-title-dark"
                                    >
                                        {{
                                            stationInfo.installedCapacity || '-'
                                        }}
                                    </div>
                                </div>
                                <a-divider type="vertical" class="h-10" />
                                <div class="flex-1 text-center">
                                    <div class="inline-block text-left">
                                        <div
                                            class="text-secondar-text dark:text-60-dark"
                                        >
                                            {{
                                                $t('station_zhuangjigonglv')
                                            }}(kW)
                                        </div>
                                        <div
                                            class="text-base font-medium text-title dark:text-title-dark"
                                        >
                                            {{
                                                stationInfo.installedPower ||
                                                '-'
                                            }}
                                        </div>
                                    </div>
                                </div>
                                <a-divider type="vertical" class="h-10" />
                                <div class="flex-1 text-right">
                                    <div class="inline-block text-left">
                                        <div
                                            class="text-secondar-text dark:text-60-dark"
                                        >
                                            {{ $t('station_touyunriqi') }}
                                        </div>
                                        <div
                                            class="text-base font-medium text-title dark:text-title-dark"
                                        >
                                            {{
                                                stationInfo.createTime
                                                    ? dayjs(
                                                          stationInfo.createTime
                                                      ).format('YYYY/MM/DD')
                                                    : '-'
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="h-7 mb-3.5">
                                <div
                                    class="px-2 py-2 bg-f5f7f7 dark:bg-ffffff-dark rounded text-secondar-text dark:text-60-dark opacity-80 overflow"
                                    v-if="getCompanyInfo?.orgType == 'supplier'"
                                >
                                    <div
                                        class="overflow float-left"
                                        style="
                                            max-width: 50%;
                                            padding-right: 2%;
                                            line-height: 14px;
                                        "
                                        :title="stationInfo.supplierName"
                                    >
                                        {{ $t('station_fuwushang') }}：{{
                                            stationInfo.supplierName
                                        }}
                                    </div>
                                    <div
                                        class="overflow float-left"
                                        style="
                                            max-width: 50%;
                                            padding-left: 2%;
                                            border-left: 1px solid #d9d9d9;
                                            line-height: 14px;
                                        "
                                        :title="stationInfo.customerName"
                                    >
                                        {{ $t('station_kehu') }} ：{{
                                            stationInfo.customerName
                                        }}
                                    </div>
                                </div>
                            </div>

                            <div
                                class="flex text-secondar-text dark:text-60-dark opacity-80"
                            >
                                <iconSvg
                                    name="ip"
                                    class="w-4 h-4"
                                    style="margin-top: 3px"
                                />
                                <div
                                    class="ml-1 w-0 flex-1 mutiLineOverflow"
                                    :title="stationInfo.address"
                                >
                                    {{ $t('station_zhandiandizhi') }}：{{
                                        stationInfo.address
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <edit-info
                        v-if="infoVisible"
                        v-model:visible="infoVisible"
                        @onClose="onClose"
                        @update="updateData"
                        :info="deviceInfo"
                    />
                </div>
                <a-divider type="vertical" class="m-0 h-52" />
                <div
                    class="pie flex items-center justify-between px-7"
                    style="width: 355px"
                >
                    <div
                        id="chartDischarge"
                        class="chartDischarge"
                        style="width: 208px; height: 208px"
                    ></div>
                    <div
                        class="echart-title text-xs space-y-2 flex-1 text-title dark:text-title-dark"
                    >
                        <div class="">
                            <span class="uppercase"> {{ $t('soh') }}</span> 「{{
                                stationInfo?.soh || stationInfo.soh == 0
                                    ? stationInfo.soh + '%'
                                    : '-'
                            }}」
                        </div>
                        <div class="">
                            <span class="uppercase"> {{ $t('soc') }}</span
                            >「{{
                                stationInfo?.soc || stationInfo?.soc == 0
                                    ? +stationInfo.soc + '%'
                                    : '-'
                            }}」
                        </div>
                        <div>
                            {{ $t('Temp') }}「{{
                                stationInfo?.avgTemperature ||
                                stationInfo?.avgTemperature == 0
                                    ? stationInfo.avgTemperature + '°C'
                                    : '-'
                            }}」
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex gap-x-3 w-full mb-5">
                <!-- 充放电 -->
                <div
                    class="w-1/2 p-3 bg-ff dark:bg-car-pie-border rounded cursor-pointer tabs-content"
                    :class="activeKey === '1' ? 'active' : ''"
                    @click="changeTab('1')"
                >
                    <div class="">
                        <div
                            class="charge-title flex justify-between text-title dark:text-title-dark"
                        >
                            <div
                                class="charge-title-l h-12 pl-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end"
                            >
                                <div class="leading-6 text-base">
                                    {{ $t('station_zuorichongdianliang') }}
                                </div>
                                <div
                                    class="text-3.5xl leading-8 font-bold"
                                    :class="locale == 'en' ? 'ml-2' : 'ml-4.5'"
                                >
                                    {{
                                        unitConversion(
                                            pieceOthenData.yesterdayCharge,
                                            1000
                                        )
                                    }}
                                </div>
                                <div class="text-xs leading-5 ml-0.5">
                                    {{
                                        alternateUnits(
                                            pieceOthenData.yesterdayCharge,
                                            1000
                                        )
                                            ? 'MWh'
                                            : 'kWh'
                                    }}
                                </div>
                            </div>
                            <div
                                class="charge-title-r h-12 pr-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end justify-end"
                            >
                                <div class="leading-6 text-base">
                                    {{ $t('station_zuorifangdianliang') }}
                                </div>
                                <div
                                    class="text-3.5xl leading-8 font-bold"
                                    :class="locale == 'en' ? 'ml-2' : 'ml-4.5'"
                                >
                                    {{
                                        unitConversion(
                                            pieceOthenData.yesterdayDischarge,
                                            1000
                                        )
                                    }}
                                </div>
                                <div class="text-xs leading-5 ml-0.5">
                                    {{
                                        alternateUnits(
                                            pieceOthenData.yesterdayDischarge,
                                            1000
                                        )
                                            ? 'MWh'
                                            : 'kWh'
                                    }}
                                </div>
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center px-3"
                            style="line-height: 42px"
                        >
                            <div class="flex-1 text-left">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        pieceOthenData?.beforeYesterdayCharge ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="pieceOthenData.comparedChargePercent"
                                />
                            </div>
                            <div class="flex-1 text-right">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        pieceOthenData?.beforeYesterdayDischarge ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        pieceOthenData.comparedDischargePercent
                                    "
                                />
                            </div>
                        </div>
                        <a-divider class="m-0" />
                        <div
                            class="flex justify-between items-center px-3 mt-3 leading-4"
                        >
                            <div class="flex-1 text-left flex">
                                <div>
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_yueleiji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            unitConversion(
                                                pieceOthenData.currentMonthCharge,
                                                1000
                                            )
                                        }}
                                        <span class="text-sm">
                                            {{
                                                alternateUnits(
                                                    pieceOthenData.currentMonthCharge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_zongji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            unitConversion(
                                                pieceOthenData.totalCharge,
                                                1000
                                            )
                                        }}
                                        <span class="text-sm">
                                            {{
                                                alternateUnits(
                                                    pieceOthenData.totalCharge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-1 text-right flex justify-end">
                                <div>
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_yueleiji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            unitConversion(
                                                pieceOthenData.currentMonthDischarge,
                                                1000
                                            )
                                        }}
                                        <span class="text-sm">
                                            {{
                                                alternateUnits(
                                                    pieceOthenData.currentMonthDischarge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_zongji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            unitConversion(
                                                pieceOthenData.totalDischarge,
                                                1000
                                            )
                                        }}
                                        <span class="text-sm">
                                            {{
                                                alternateUnits(
                                                    pieceOthenData.totalDischarge,
                                                    1000
                                                )
                                                    ? 'MWh'
                                                    : 'kWh'
                                            }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-1/2 flex gap-x-3">
                    <!-- 收益 -->
                    <div
                        class="flex-1 p-3 bg-ff dark:bg-car-pie-border rounded cursor-pointer tabs-content"
                        :class="activeKey == '2' ? 'active' : ''"
                        @click.stop="changeTab('2')"
                    >
                        <div class="flex gap-x-4">
                            <div class="flex-1">
                                <div
                                    class="h-12 leading-12 rounded text-sm font-medium text-title dark:text-title-dark"
                                >
                                    {{ $t('station_zuorishouyi') }}
                                    <price
                                        numClassName="text-2.5xl font-bold ml-1"
                                        unitClassName=""
                                        :price="pieceOthenData.yesterdayProfit"
                                    />
                                </div>
                                <div class="flex-1" style="line-height: 42px">
                                    <span
                                        class="text-secondar-text dark:text-60-dark"
                                    >
                                        {{
                                            pieceOthenData?.beforeYesterdayProfit ==
                                            0
                                                ? $t('Previous day: No data')
                                                : $t('station_jiaoqianyiri') +
                                                  '：'
                                        }}
                                    </span>
                                    <percentage
                                        :num="
                                            pieceOthenData.comparedProfitPercent
                                        "
                                    />
                                </div>
                                <a-divider class="m-0" />
                                <div
                                    class="flex justify-between mt-3 leading-4"
                                >
                                    <div>
                                        <div
                                            class="text-secondar-text dark:text-60-dark mb-2"
                                        >
                                            {{ $t('station_yueleiji') }}
                                        </div>
                                        <div
                                            class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                        >
                                            <price
                                                numClassName="font-medium text-base leading-4"
                                                unitClassName="font-medium text-base leading-4"
                                                :price="
                                                    pieceOthenData.currentMonthProfit
                                                "
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <div
                                            class="text-secondar-text dark:text-60-dark mb-2 text-right"
                                        >
                                            {{ $t('station_zongji') }}
                                        </div>
                                        <div
                                            class="text-title dark:text-title-dark"
                                        >
                                            <price
                                                numClassName="font-medium text-base leading-4"
                                                :price="
                                                    pieceOthenData.totalProfit
                                                "
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                v-if="isDemoUser && 1 > 2"
                                class="rounded bg-background px-4 py-4"
                                style="width: 196px"
                            >
                                <div
                                    class="text-sm font-medium flex items-center justify-between"
                                >
                                    <div>虚拟电厂</div>
                                    <div
                                        v-if="completeVpp"
                                        class="w-5 h-5"
                                        @click.stop="AiOptimize"
                                    >
                                        <iconSvg
                                            name="shezhi"
                                            className="w-5 h-5"
                                            style="height: 20px"
                                        />
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <!-- 未开启 -->
                                    <template v-if="!vppInfo.openVpp">
                                        <div
                                            class="text-center items-center rounded-full inline-flex gap-x-2 text-ff px-3 h-8 select-none cursor-pointer"
                                            :class="
                                                stationInfo.status === 3
                                                    ? 'opacity-40 cursor-not-allowed'
                                                    : ''
                                            "
                                            style="background: #7d4af9"
                                            @click.stop="openAiModal"
                                        >
                                            <img
                                                src="@/assets/device/ai.png"
                                                class="w-5 h-5"
                                                alt=""
                                                srcset=""
                                            />
                                            <div>启用AI模式</div>
                                        </div>
                                        <div
                                            class="text-secondar-text dark:text-60-dark mt-4"
                                        >
                                            启用会进入数据训练阶段，预计需要15天
                                        </div>
                                    </template>
                                    <!-- 开启 -->
                                    <template v-else>
                                        <div v-if="!completeVpp">
                                            <!-- 未完成 -->
                                            <div
                                                class="flex items-center gap-x-2"
                                            >
                                                <img
                                                    src="@/assets/device/ai.png"
                                                    class="w-4 h-4"
                                                    alt=""
                                                    srcset=""
                                                />
                                                <div class="progress flex-1">
                                                    <div
                                                        class="w-full rounded-full bg-background h-5 border-transparent"
                                                        style="
                                                            border-width: 3px;
                                                        "
                                                    >
                                                        <div
                                                            class="h-full rounded-full"
                                                            :style="{
                                                                width: `${
                                                                    ((15 -
                                                                        dayDiff) /
                                                                        15 >
                                                                    1
                                                                        ? 1
                                                                        : (15 -
                                                                              dayDiff) /
                                                                              15 <
                                                                          0.12
                                                                        ? 0.12
                                                                        : (15 -
                                                                              dayDiff) /
                                                                          15) *
                                                                    100
                                                                }%`,
                                                                background:
                                                                    '#7D4AF9',
                                                            }"
                                                        ></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-xs mt-4 leading-5">
                                                虚拟电厂电站数据训练中<br />预计剩余{{
                                                    dayDiff
                                                }}天
                                            </div>
                                        </div>
                                        <div v-else>
                                            <!-- 完成 -->
                                            <div
                                                class="flex justify-between items-center mb-1"
                                            >
                                                <div class="flex items-center">
                                                    <div
                                                        class="text-secondar-text dark:text-60-dark"
                                                    >
                                                        需求响应：
                                                    </div>
                                                    <div @click.stop>
                                                        <a-switch
                                                            class="w-10 h-6 switch ml-1"
                                                            :checkedValue="1"
                                                            :unCheckedValue="0"
                                                            :loading="
                                                                switcLoading
                                                            "
                                                            @change="
                                                                changeDemandResponse
                                                            "
                                                            v-model:checked="
                                                                vppInfo.openVppDemand
                                                            "
                                                        />
                                                    </div>
                                                </div>
                                                <img
                                                    src="@/assets/device/ai.png"
                                                    class="w-5 h-5"
                                                    alt=""
                                                    srcset=""
                                                />
                                            </div>
                                            <div>
                                                参与结算次数
                                                {{
                                                    responseInfo.settlementCount
                                                }}次
                                            </div>
                                            <div>
                                                总结算电量
                                                {{
                                                    unitConversion(
                                                        responseInfo.totalNgy,
                                                        10000
                                                    )
                                                }}{{
                                                    alternateUnits(
                                                        responseInfo.totalNgy,
                                                        10000
                                                    )
                                                        ? 'MWh'
                                                        : 'kWh'
                                                }}
                                            </div>
                                            <div>
                                                总收益

                                                <price
                                                    :price="
                                                        responseInfo.totalAmount
                                                    "
                                                />
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 异常 -->
                    <!-- <div
                        class="w-1/3 p-3 bg-ff dark:bg-ff-dark rounded cursor-pointer tabs-content"
                        :class="activeKey == '3' ? 'active' : ''"
                        @click="changeTab('3')"
                    >
                        <div
                            class="rounded text-sm h-12 leading-12 font-medium flex justify-between items-center"
                        >
                            <div class="pl-3">
                                当前异常：<span
                                    class="text-2.5xl font-bold ml-1"
                                >
                                    {{ alarmData?.processingQuantity }}</span
                                >个
                            </div>
                            <i
                                class="iconfont icon-a-ica-dianchi-guzhangbeifen16 text-2xl"
                                style="color: rgb(253, 11, 11)"
                            ></i>
                        </div>
                        <div
                            class="bg-ff dark:bg-ff-dark px-3 rounded text-secondar-text dark:text-60-dark"
                            style="line-height: 42px; height: 42px"
                        >
                        </div>
                        <a-divider class="m-0" />
                        <div class="flex gap-x-5 mt-3 leading-4 pl-3">
                            <div>
                                <div class="text-secondar-text dark:text-60-dark mb-2">
                                    今日新增
                                </div>
                                <div class="font-medium text-base leading-4">
                                    {{ alarmData?.todayQuantity }}
                                </div>
                            </div>
                            <div>
                                <div class="text-secondar-text dark:text-60-dark mb-2">
                                    七日新增
                                </div>
                                <div class="font-medium text-base leading-4">
                                    {{ alarmData?.sevenDayQuantity }}
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="topTabs">
                <a-tabs class="w-full tabs pt-1" :activeKey="activeKey">
                    <a-tab-pane key="1" tab="a">
                        <div class="gap-x-3 mb-3">
                            <div
                                class="bg-ff dark:bg-car-pie-border rounded-lg p-4 mb-3"
                            >
                                <div
                                    class="flex justify-between items-center mb-3 text-title dark:text-title-dark"
                                >
                                    <div>
                                        {{ $t('station_chongfangdiantongji') }}
                                    </div>
                                    <div class="flex items-center gap-x-3">
                                        <!--   -->
                                        <date-search
                                            :info="{
                                                periodOptions:
                                                    emsInfo?.emsType ==
                                                        'mingWork' ||
                                                    emsInfo?.emsType ==
                                                        'mingwork'
                                                        ? [
                                                              'hour',
                                                              'day',
                                                              'month',
                                                          ]
                                                        : ['day', 'month'],
                                                datePickerType: 'minute',
                                                defaultPickerType: 'day',
                                                dayRangeLen: 30,
                                                defaultDayRangeLen: 7,
                                                minDate: moment(
                                                    stationInfo.createTime
                                                ).format('YYYY-MM-DD'),
                                            }"
                                            @onChange="dateSearchChange"
                                            v-model:dateSelect="dateSelect"
                                        />
                                        <toggleView
                                            @change="onChangeView"
                                            v-model:type="chargeViewType"
                                        />
                                        <el-popconfirm
                                            :title="
                                                $t('export_tips01')
                                                    .replace(
                                                        's%',
                                                        dateSelect?.startDate ||
                                                            dateSelect?.startMonth
                                                    )
                                                    .replace(
                                                        's%',
                                                        dateSelect?.endDate ||
                                                            dateSelect?.endMonth
                                                    )
                                            "
                                            :confirm-button-text="
                                                $t('common_shi')
                                            "
                                            :cancel-button-text="
                                                $t('common_fou')
                                            "
                                            plain
                                            confirm-button-type="primary"
                                            width="414"
                                            @confirm="exportChart"
                                            placement="bottom-end"
                                        >
                                            <template #reference>
                                                <export-button
                                                    style="margin-left: 0"
                                                />
                                            </template>
                                        </el-popconfirm>
                                    </div>
                                </div>

                                <div
                                    class="relative"
                                    style="height: 385px"
                                    id="widthBox"
                                    v-if="chargeViewType == 'chart'"
                                >
                                    <div
                                        id="chargingStatisticsEchart"
                                        class="w-full z-10"
                                        :style="{
                                            height:
                                                dateSelect?.periodType ===
                                                    'hour' &&
                                                segmentTypeByHour1.length > 0
                                                    ? '347px'
                                                    : '385px',
                                        }"
                                    ></div>
                                    <div
                                        class="absolute left-0 top-0 w-full h-full z-0 segmentTypeByHourDiv overflow-x-hidden"
                                        v-if="
                                            dateSelect?.periodType === 'hour' &&
                                            segmentTypeByHour1.length > 0
                                        "
                                    >
                                        <div
                                            class="absolute left-0 top-0 w-full h-full pl-10 z-0 pr-5"
                                            id="segmentTypeByHourWidth"
                                            :style="{
                                                width: segmentTypeByHourWidth,
                                            }"
                                        >
                                            <div class="h-full">
                                                <div
                                                    class="h-full flex w-full"
                                                    style="margin: 0 auto"
                                                >
                                                    <div
                                                        v-for="item in segmentTypeByHour1"
                                                        :key="item.hour"
                                                        class=""
                                                        :style="{
                                                            width: item.percentage,
                                                            height: '100%',
                                                        }"
                                                    >
                                                        <div
                                                            class="w-full relative opacity-40"
                                                            :style="{
                                                                height: '347px',
                                                                ...item.colors,
                                                            }"
                                                        >
                                                            <div
                                                                class="w-2 h-2 rounded-full absolute -left-1 -bottom-1 bg-ff dark:bg-ff-dark"
                                                                :style="{
                                                                    border:
                                                                        '1px solid ' +
                                                                        item
                                                                            .colors
                                                                            .color,
                                                                }"
                                                            ></div>
                                                            <div
                                                                class="w-2 h-2 rounded-full absolute -right-1 -bottom-1 bg-ff dark:bg-ff-dark"
                                                                :style="{
                                                                    border:
                                                                        '1px solid ' +
                                                                        item
                                                                            .colors
                                                                            .color,
                                                                }"
                                                            ></div>
                                                        </div>
                                                        <div
                                                            class="text-center text-primary-text dark:text-80-dark text-xs mt-1"
                                                            :style="{
                                                                color: item
                                                                    .colors
                                                                    .color,
                                                            }"
                                                        >
                                                            {{ item.text }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="relative"
                                    style="height: 385px"
                                    id="widthBox"
                                    v-if="chargeViewType == 'table'"
                                >
                                    <!-- :loading="tableLoading" -->
                                    <ChargeData
                                        class="h-full"
                                        v-model:tableData="chargeTableData"
                                    />
                                </div>
                            </div>
                            <div
                                class="bg-ff dark:bg-car-pie-border rounded-lg p-4"
                            >
                                <div
                                    class="mb-3 leading-8 text-title dark:text-title-dark"
                                >
                                    {{ $t('station_zhengtiyunxingzhuangtai') }}
                                </div>
                                <div
                                    style="height: 347px"
                                    class="p-7 pb-2.5 border border-border rounded"
                                >
                                    <working-status
                                        :bmsPower="stationInfo.bmsPower"
                                        :gridPower="stationInfo.gridPower"
                                        :status="stationInfo.status"
                                    />
                                </div>
                            </div>
                        </div>
                        <!-- ai -->
                        <!-- // 训练完成在展示 -->
                        <div
                            class="mb-3 flex gap-x-3"
                            v-if="vppInfo.aiTrainStatus && 1 > 2"
                        >
                            <div
                                class="flex-1 bg-ff dark:bg-car-pie-border rounded-lg p-4"
                            >
                                <div class="flex">
                                    <div class="mb-4 flex items-center gap-x-2">
                                        <div>今日运行轨迹预测</div>
                                        <img
                                            src="@/assets/device/ai.png"
                                            class="w-5 h-5"
                                            alt=""
                                            srcset=""
                                        />
                                    </div>
                                </div>
                                <div class="flex mb-6 gap-x-15">
                                    <div class="">
                                        <div class="inline-block text-left">
                                            <div
                                                class="text-secondar-text dark:text-60-dark"
                                            >
                                                实时累计充电量(
                                                {{
                                                    alternateUnits(
                                                        RealStrategyAndAiRecommendStrategy.realCharge ||
                                                            0,
                                                        1000
                                                    )
                                                        ? 'MWh'
                                                        : 'kWh'
                                                }})
                                            </div>
                                            <div class="text-base font-medium">
                                                {{
                                                    unitConversion(
                                                        RealStrategyAndAiRecommendStrategy.realCharge ||
                                                            0,
                                                        1000
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                    <a-divider type="vertical" class="h-12" />
                                    <div class="text-center">
                                        <div class="inline-block text-left">
                                            <div
                                                class="text-secondar-text dark:text-60-dark"
                                            >
                                                预测充电量(
                                                {{
                                                    alternateUnits(
                                                        RealStrategyAndAiRecommendStrategy.aiRecommendCharge ||
                                                            0,
                                                        1000
                                                    )
                                                        ? 'MWh'
                                                        : 'kWh'
                                                }})
                                            </div>
                                            <div class="text-base font-medium">
                                                {{
                                                    unitConversion(
                                                        RealStrategyAndAiRecommendStrategy.aiRecommendCharge ||
                                                            0,
                                                        1000
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                    <a-divider type="vertical" class="h-12" />
                                    <div class="text-right">
                                        <div class="inline-block text-left">
                                            <div
                                                class="text-secondar-text dark:text-60-dark"
                                            >
                                                实时累计放电量({{
                                                    alternateUnits(
                                                        RealStrategyAndAiRecommendStrategy.realDischarge ||
                                                            0,
                                                        1000
                                                    )
                                                        ? 'MWh'
                                                        : 'kWh'
                                                }})
                                            </div>
                                            <div class="text-base font-medium">
                                                {{
                                                    unitConversion(
                                                        RealStrategyAndAiRecommendStrategy.realDischarge ||
                                                            0,
                                                        1000
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                    <a-divider type="vertical" class="h-12" />
                                    <div class="text-right">
                                        <div class="inline-block text-left">
                                            <div
                                                class="text-secondar-text dark:text-60-dark"
                                            >
                                                预测放电量({{
                                                    alternateUnits(
                                                        RealStrategyAndAiRecommendStrategy.aiRecommendDischarge ||
                                                            0,
                                                        1000
                                                    )
                                                        ? 'MWh'
                                                        : 'kWh'
                                                }})
                                            </div>
                                            <div class="text-base font-medium">
                                                {{
                                                    unitConversion(
                                                        RealStrategyAndAiRecommendStrategy.aiRecommendDischarge ||
                                                            0,
                                                        1000
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <running-trajectory
                                        :data="
                                            RealStrategyAndAiRecommendStrategy
                                        "
                                    />
                                </div>
                            </div>
                            <!-- <div class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4">
                                <div class="flex">
                                    <div class="mb-4">电池衰减趋势</div>
                                </div>
                            </div> -->
                        </div>
                        <div class="mb-3 flex gap-x-3">
                            <div
                                class="flex-1 bg-ff dark:bg-car-pie-border my-m-r-3 relative my-rounded-lg overflow-hidden pb-4"
                            >
                                <div
                                    class="flex justify-between items-center p-4"
                                >
                                    <div
                                        class="left-4 title-family text-title dark:text-title-dark"
                                    >
                                        {{
                                            $t(
                                                'station_24xiaoshidianliangbianhua'
                                            )
                                        }}
                                    </div>
                                    <div class="mr-5">
                                        <date-search
                                            :info="{
                                                periodOptions: [],
                                                datePickerType: 'hour',
                                                minDate: moment(
                                                    stationInfo.createTime
                                                ).format('YYYY-MM-DD'),
                                            }"
                                            @onChange="pickChange"
                                            v-model:dateSelect="pickerTime"
                                        />
                                    </div>
                                </div>
                                <div class="text-center mb-1.5">
                                    <div
                                        class="top-4 title-family flex justify-center items-center leading-6 text-title dark:text-title-dark"
                                    >
                                        <span class="mr-4 flex items-center"
                                            ><span class="raduis-box"></span
                                            ><span class="ml-1">{{
                                                $t('soc')
                                            }}</span></span
                                        >
                                        <span class="mr-4 flex items-center"
                                            ><img
                                                src="@/assets/device/lightning-3.png"
                                            /><span class="ml-1">{{
                                                $t('status_chongdian')
                                            }}</span></span
                                        >
                                        <span class="flex items-center"
                                            ><img
                                                src="@/assets/device/lightning-4.png"
                                            /><span class="ml-1">{{
                                                $t('status_fangdian')
                                            }}</span></span
                                        >
                                    </div>
                                </div>
                                <div class="demo-echart">
                                    <div
                                        id="demo"
                                        style="height: 303px; width: 100%"
                                    ></div>
                                </div>
                            </div>
                            <div
                                class="flex-1 bg-ff dark:bg-car-pie-border relative my-rounded-lg overflow-hidden"
                            >
                                <div
                                    class="flex justify-between items-center p-4"
                                >
                                    <div
                                        class="top-4 left-4 title-family text-title dark:text-title-dark"
                                    >
                                        {{ $t('station_xitongxiaolv') }}
                                    </div>
                                    <div
                                        class="top-3 right-5 flex items-center"
                                    >
                                        <date-search
                                            :info="{
                                                periodOptions: [],
                                                datePickerType: 'day',
                                                dayRangeLen: 7,
                                                minDate: moment(
                                                    stationInfo.createTime
                                                ).format('YYYY-MM-DD'),
                                            }"
                                            @onChange="efficiencyChange"
                                            v-model:dateSelect="
                                                efficiencySelect
                                            "
                                        />
                                        <el-popconfirm
                                            :title="
                                                $t('export_tips01')
                                                    .replace(
                                                        's%',
                                                        efficiencySelect?.startDate ||
                                                            efficiencySelect?.startMonth
                                                    )
                                                    .replace(
                                                        's%',
                                                        efficiencySelect?.endDate ||
                                                            efficiencySelect?.endMonth
                                                    )
                                            "
                                            :confirm-button-text="
                                                $t('common_shi')
                                            "
                                            :cancel-button-text="
                                                $t('common_fou')
                                            "
                                            width="240"
                                            @confirm="confirmExport"
                                        >
                                            <template #reference>
                                                <export-button />
                                            </template>
                                        </el-popconfirm>
                                    </div>
                                </div>
                                <div
                                    id="lineCharts"
                                    style="height: 333px; width: 100%"
                                ></div>
                            </div>
                        </div>

                        <div
                            class="mb-3 flex flex-col bg-ff dark:bg-car-pie-border my-rounded-lg overflow-hidden"
                            id="power"
                        >
                            <div class="flex justify-between items-center p-4">
                                <div
                                    class="title-family text-title dark:text-title-dark"
                                >
                                    {{ $t('station_gonglüqushifenxi') }}
                                </div>
                                <div>
                                    <el-select
                                        class="w40 mr-4"
                                        :model-value="'minute'"
                                    >
                                        <el-option
                                            key="minute"
                                            :label="
                                                $t('station_fenzhonghuizong')
                                            "
                                            value="minute"
                                        />
                                    </el-select>
                                    <el-date-picker
                                        v-model="powerDate"
                                        type="daterange"
                                        range-separator="-"
                                        :start-placeholder="
                                            $t('common_kaishiriqi')
                                        "
                                        :end-placeholder="
                                            $t('common_jieshuriqi')
                                        "
                                        value-format="YYYY-MM-DD"
                                        @calendar-change="onCalendarChange"
                                        :disabled-date="disabledDates"
                                        @change="powerDateChange"
                                        @visible-change="onVisibleChange"
                                        style="width: 240px"
                                        :clearable="false"
                                    />
                                    <el-popconfirm
                                        :title="
                                            $t('export_tips01')
                                                .replace(
                                                    's%',
                                                    powerDate[0] ||
                                                        moment().format(
                                                            'YYYY-MM-DD'
                                                        )
                                                )
                                                .replace(
                                                    's%',
                                                    powerDate[1] ||
                                                        moment().format(
                                                            'YYYY-MM-DD'
                                                        )
                                                )
                                        "
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                        @confirm="confirmExportGl"
                                    >
                                        <template #reference>
                                            <export-button />
                                        </template>
                                    </el-popconfirm>
                                </div>
                            </div>
                            <div class="flex-1">
                                <a-spin :spinning="spinning">
                                    <div
                                        class="e-height-s"
                                        id="powerLine"
                                        style="width: 100%; height: 300px"
                                    ></div>
                                </a-spin>
                            </div>
                        </div>
                        <div
                            class="bg-ff dark:bg-car-pie-border pt-0 my-rounded-lg overflow-hidden device-tabs mb-3"
                            v-if="containerList.length > 0"
                        >
                            <!-- 机柜标题 -->
                            <a-tabs
                                class="blockTabsb flex-1 flex flex-col translateTabs"
                                v-model:activeKey="tabCabinetActive"
                                @change="tabChange"
                            >
                                <template #tabBarExtraContent>
                                    <el-popconfirm
                                        :title="
                                            $t(
                                                'Are you sure you want to reset?'
                                            )
                                        "
                                        @confirm="
                                            handleReset('BMS', 'bmsFaultReset')
                                        "
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                    >
                                        <template #reference>
                                            <el-button
                                                plain
                                                round
                                                class="border-1 mr-3 relative"
                                                v-if="
                                                    (emsInfo?.emsType ==
                                                        'mingWork' ||
                                                        emsInfo?.emsType ==
                                                            'mingwork') &&
                                                    isEmsControl
                                                "
                                                >{{
                                                    $t('station_bmsfuwei')
                                                }}</el-button
                                            >
                                        </template>
                                    </el-popconfirm>
                                    <el-popconfirm
                                        :title="
                                            $t(
                                                'Are you sure you want to reset?'
                                            )
                                        "
                                        @confirm="
                                            handleReset(
                                                'SXBLQ',
                                                'pcsFaultReset'
                                            )
                                        "
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                    >
                                        <template #reference>
                                            <el-button
                                                plain
                                                round
                                                class="border-1 mr-3 relative"
                                                v-if="
                                                    (emsInfo?.emsType ==
                                                        'mingWork' ||
                                                        emsInfo?.emsType ==
                                                            'mingwork') &&
                                                    isEmsControl
                                                "
                                                >{{
                                                    $t('station_pcsfuwei')
                                                }}</el-button
                                            >
                                        </template>
                                    </el-popconfirm>
                                    <el-popconfirm
                                        :title="
                                            $t(
                                                'Are you sure you want to reset?'
                                            )
                                        "
                                        @confirm="
                                            handleReset('YLJ', 'yljFaultReset')
                                        "
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                    >
                                        <template #reference>
                                            <el-button
                                                plain
                                                round
                                                class="border-1 mr-3 relative"
                                                v-if="
                                                    (emsInfo?.emsType ==
                                                        'mingWork' ||
                                                        emsInfo?.emsType ==
                                                            'mingwork') &&
                                                    isEmsControl
                                                "
                                                >{{
                                                    $t('station_yelengjifuwei')
                                                }}</el-button
                                            >
                                        </template>
                                    </el-popconfirm>
                                    <!-- <el-button
                                        plain
                                        round
                                        @click="lookDetail('other')"
                                        class="border-1 mr-3 relative btn-hover"
                                    >
                                        <span>查看明细</span>
                                        <span class="icon-box ml-0.5">
                                            <iconSvg
                                                name="search"
                                                class="icon-default"
                                            />
                                        </span>
                                    </el-button> -->
                                    <toggleView
                                        @change="onChangeDeviceDataView"
                                        v-model:type="deviceDataViewType"
                                        class="align-middle mr-4"
                                    />
                                    <el-button
                                        plain
                                        round
                                        @click="loadingData"
                                        class="border-1 mr-3 relative"
                                    >
                                        <div class="flex items-center">
                                            <span>{{ $t('Refresh') }}</span>

                                            <!-- <SyncOutlined /> -->
                                            <iconSvg
                                                name="refresh"
                                                class="icon-default w-4 h-4 leading-4 ml-0.5"
                                            />
                                        </div>
                                    </el-button>
                                </template>
                                <a-tab-pane
                                    :key="item.containerNo"
                                    :tab="item.title"
                                    v-for="item in containerList"
                                />
                            </a-tabs>
                            <a-spin
                                :spinning="cabineLoading"
                                v-if="deviceDataViewType == 'chart'"
                            >
                                <!--  v-if="productModel == 'SE215'" -->
                                <div class="pb-3">
                                    <div class="tab-content mb-3">
                                        <div class="flex flex-wrap">
                                            <div class="flex-1-width">
                                                <template
                                                    v-if="
                                                        productModel == 'SE215'
                                                    "
                                                >
                                                    <TranslateBox
                                                        :headers="false"
                                                    >
                                                        <template #content>
                                                            <div
                                                                class="translate-svg"
                                                            >
                                                                <svgComments
                                                                    @svgClick="
                                                                        svgClick
                                                                    "
                                                                    :propActive="
                                                                        propActive
                                                                    "
                                                                    ref="svgCommentBox"
                                                                />
                                                            </div>
                                                        </template>
                                                    </TranslateBox>
                                                </template>
                                                <template
                                                    v-else-if="
                                                        productModel == 'SE70'
                                                    "
                                                >
                                                    <div
                                                        class="flex pl-6 pt-6 pb-8 cabinet"
                                                    >
                                                        <div class="se701">
                                                            <img
                                                                class="w-full h-full relative"
                                                                style="
                                                                    z-index: 1;
                                                                "
                                                                src="@/assets/device/SE70-1.png"
                                                                alt=""
                                                            />

                                                            <div
                                                                class="se70-box"
                                                                style=""
                                                            >
                                                                <div
                                                                    class="se70-bg"
                                                                    :class="{
                                                                        active:
                                                                            selectBox ===
                                                                            1,
                                                                    }"
                                                                    @click="
                                                                        DeviceClick(
                                                                            {
                                                                                showDetailBox: 1,
                                                                            }
                                                                        )
                                                                    "
                                                                >
                                                                    <div
                                                                        class="block0 block1"
                                                                    ></div>
                                                                    <div
                                                                        class="block0 block2"
                                                                    ></div>
                                                                    <div
                                                                        class="block0 block25"
                                                                    ></div>
                                                                    <div
                                                                        class="block0 block3"
                                                                    ></div>
                                                                    <div
                                                                        class="block0 block4"
                                                                    ></div>
                                                                    <div
                                                                        class="block0 block5"
                                                                    ></div>
                                                                    <div
                                                                        class="block0 block6"
                                                                    ></div>
                                                                </div>
                                                                <a-tooltip
                                                                    placement="right"
                                                                >
                                                                    <template
                                                                        #title
                                                                    >
                                                                        <span>{{
                                                                            $t(
                                                                                'station_bianliuqi'
                                                                            )
                                                                        }}</span>
                                                                    </template>
                                                                    <div
                                                                        @click="
                                                                            DeviceClick(
                                                                                {
                                                                                    showDetailBox: 2,
                                                                                }
                                                                            )
                                                                        "
                                                                        class="pcs flex items-center justify-center"
                                                                        :class="{
                                                                            active:
                                                                                selectBox ===
                                                                                2,
                                                                        }"
                                                                    >
                                                                        {{
                                                                            selectBox ===
                                                                            2
                                                                                ? $t(
                                                                                      'station_bianliuqi'
                                                                                  )
                                                                                : ''
                                                                        }}
                                                                    </div>
                                                                </a-tooltip>

                                                                <div
                                                                    class="batterys"
                                                                >
                                                                    <div
                                                                        class="battery-main flex items-center justify-center"
                                                                        :class="{
                                                                            active:
                                                                                selectBox ===
                                                                                99,
                                                                        }"
                                                                        @click="
                                                                            DeviceClick(
                                                                                {
                                                                                    showDetailBox: 99,
                                                                                }
                                                                            )
                                                                        "
                                                                    >
                                                                        {{
                                                                            selectBox ===
                                                                            99
                                                                                ? $t(
                                                                                      'station_dianchiguanlixitong'
                                                                                  )
                                                                                : ''
                                                                        }}
                                                                    </div>
                                                                    <div
                                                                        class="flex-1"
                                                                    >
                                                                        <div
                                                                            class="battery"
                                                                            v-for="item in [
                                                                                10,
                                                                                9,
                                                                                8,
                                                                                7,
                                                                                6,
                                                                            ]"
                                                                            :key="
                                                                                item
                                                                            "
                                                                        >
                                                                            <a-tooltip
                                                                                placement="left"
                                                                            >
                                                                                <template
                                                                                    #title
                                                                                >
                                                                                    <span>
                                                                                        {{
                                                                                            $t(
                                                                                                'dianchibao'
                                                                                            ).replace(
                                                                                                's%',
                                                                                                item
                                                                                            )
                                                                                        }}
                                                                                    </span>
                                                                                </template>
                                                                                <div
                                                                                    class="detail flex items-center justify-center"
                                                                                    :class="{
                                                                                        active:
                                                                                            selectBox ===
                                                                                                5 &&
                                                                                            selectBattery ==
                                                                                                item,
                                                                                    }"
                                                                                    @click="
                                                                                        DeviceClick(
                                                                                            {
                                                                                                showDetailBox: 5,
                                                                                                containerNo:
                                                                                                    item,
                                                                                            }
                                                                                        )
                                                                                    "
                                                                                >
                                                                                    {{
                                                                                        selectBox ===
                                                                                            5 &&
                                                                                        selectBattery ==
                                                                                            item
                                                                                            ? $t(
                                                                                                  'dianchibao'
                                                                                              ).replace(
                                                                                                  's%',
                                                                                                  item
                                                                                              )
                                                                                            : ''
                                                                                    }}
                                                                                </div>
                                                                            </a-tooltip>
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="flex-1"
                                                                    >
                                                                        <div
                                                                            class="battery"
                                                                            v-for="item in [
                                                                                1,
                                                                                2,
                                                                                3,
                                                                                4,
                                                                                5,
                                                                            ]"
                                                                            :key="
                                                                                item
                                                                            "
                                                                        >
                                                                            <a-tooltip
                                                                                placement="right"
                                                                            >
                                                                                <template
                                                                                    #title
                                                                                >
                                                                                    <span>
                                                                                        {{
                                                                                            $t(
                                                                                                'dianchibao'
                                                                                            ).replace(
                                                                                                's%',
                                                                                                item
                                                                                            )
                                                                                        }}</span
                                                                                    >
                                                                                </template>
                                                                                <div
                                                                                    class="detail flex items-center justify-center"
                                                                                    :class="{
                                                                                        active:
                                                                                            selectBox ===
                                                                                                5 &&
                                                                                            selectBattery ==
                                                                                                item,
                                                                                    }"
                                                                                    @click="
                                                                                        DeviceClick(
                                                                                            {
                                                                                                showDetailBox: 5,
                                                                                                containerNo:
                                                                                                    item,
                                                                                            }
                                                                                        )
                                                                                    "
                                                                                >
                                                                                    {{
                                                                                        selectBox ===
                                                                                            5 &&
                                                                                        selectBattery ==
                                                                                            item
                                                                                            ? $t(
                                                                                                  'dianchibao'
                                                                                              ).replace(
                                                                                                  's%',
                                                                                                  item
                                                                                              )
                                                                                            : ''
                                                                                    }}
                                                                                </div>
                                                                            </a-tooltip>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div
                                                            style="
                                                                margin-left: 52px;
                                                            "
                                                            class="se702"
                                                        >
                                                            <img
                                                                src="@/assets/device/SE70-2.png"
                                                                alt=""
                                                            />
                                                            <div
                                                                class="se70-box"
                                                                style=""
                                                            >
                                                                <div
                                                                    class="se70-bg"
                                                                ></div>
                                                                <a-tooltip
                                                                    placement="left"
                                                                >
                                                                    <template
                                                                        #title
                                                                    >
                                                                        <span>{{
                                                                            $t(
                                                                                'station_dianbiao'
                                                                            )
                                                                        }}</span>
                                                                    </template>
                                                                    <div
                                                                        class="ammeter flex justify-center items-center font-medium text-ff"
                                                                        :class="{
                                                                            active:
                                                                                selectBox ===
                                                                                4,
                                                                        }"
                                                                        @click="
                                                                            DeviceClick(
                                                                                {
                                                                                    showDetailBox: 4,
                                                                                }
                                                                            )
                                                                        "
                                                                    >
                                                                        {{
                                                                            selectBox ===
                                                                            4
                                                                                ? $t(
                                                                                      'station_dianbiao'
                                                                                  )
                                                                                : ''
                                                                        }}
                                                                    </div>
                                                                </a-tooltip>
                                                                <a-tooltip
                                                                    placement="left"
                                                                >
                                                                    <template
                                                                        #title
                                                                    >
                                                                        <span>{{
                                                                            $t(
                                                                                'station_donghuanguanlixitong'
                                                                            )
                                                                        }}</span>
                                                                    </template>
                                                                    <div
                                                                        class="dynamicRing"
                                                                        :class="{
                                                                            active:
                                                                                selectBox ===
                                                                                3,
                                                                        }"
                                                                        @click="
                                                                            DeviceClick(
                                                                                {
                                                                                    showDetailBox: 3,
                                                                                }
                                                                            )
                                                                        "
                                                                    >
                                                                        {{
                                                                            selectBox ===
                                                                            3
                                                                                ? $t(
                                                                                      'station_donghuanguanlixitong'
                                                                                  )
                                                                                : ''
                                                                        }}
                                                                    </div>
                                                                </a-tooltip>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                            <div class="flex-1">
                                                <TranslateBox
                                                    :objectStyle="{
                                                        height: '100%',
                                                    }"
                                                    v-if="showBox == 1"
                                                    :headers="false"
                                                >
                                                    <template #detail>
                                                        <ul
                                                            class="flex bg-title-t title-family"
                                                        >
                                                            <li class="flex-1">
                                                                <span
                                                                    class="text-sm"
                                                                    >{{
                                                                        $t(
                                                                            'station_yunxingzhuangtai'
                                                                        )
                                                                    }}：</span
                                                                ><span
                                                                    class="origin"
                                                                    v-if="
                                                                        bmsInfoData?.runStatus ||
                                                                        bmsInfoData.runStatus ==
                                                                            0
                                                                    "
                                                                ></span
                                                                ><span
                                                                    class="ml-1 text-sm text-59"
                                                                    >{{
                                                                        bmsInfoData?.runStatus ||
                                                                        bmsInfoData.runStatus ==
                                                                            0
                                                                            ? runningStatus[
                                                                                  bmsInfoData
                                                                                      .runStatus
                                                                              ]
                                                                            : '-'
                                                                    }}</span
                                                                >
                                                            </li>
                                                            <li class="flex-1">
                                                                <span
                                                                    class="text-sm"
                                                                    >{{
                                                                        $t(
                                                                            'station_chongfangdianzhuangtai'
                                                                        )
                                                                    }}：</span
                                                                ><span
                                                                    class="ml-1 text-sm"
                                                                    >{{
                                                                        bmsInfoData?.chargeStatus ||
                                                                        bmsInfoData.chargeStatus ==
                                                                            0
                                                                            ? batteryStatus[
                                                                                  bmsInfoData
                                                                                      .chargeStatus
                                                                              ]
                                                                            : '-'
                                                                    }}</span
                                                                >
                                                            </li>
                                                        </ul>
                                                    </template>
                                                    <template #content>
                                                        <div
                                                            class="content-desc"
                                                        >
                                                            <bmsBox
                                                                :objectStyle="{
                                                                    height: '100%',
                                                                    padding:
                                                                        '16px 0',
                                                                }"
                                                                :data="
                                                                    bmsInfoData
                                                                "
                                                                @showBox="
                                                                    emitShowAnalyze
                                                                "
                                                            />
                                                        </div>
                                                    </template>
                                                </TranslateBox>

                                                <TranslateBox
                                                    :objectStyle="{
                                                        height: '100%',
                                                    }"
                                                    v-if="showBox == 2"
                                                    :headers="false"
                                                >
                                                    <template #detail>
                                                        <ul
                                                            class="flex bg-title-t"
                                                        >
                                                            <li class="flex-1">
                                                                <span
                                                                    class="text-sm"
                                                                    >{{
                                                                        $t(
                                                                            'station_yunxingzhuangtai'
                                                                        )
                                                                    }}：</span
                                                                ><span
                                                                    class="origin"
                                                                    v-if="
                                                                        pcsInfoData?.errorStatus ||
                                                                        pcsInfoData.errorStatus ==
                                                                            0
                                                                    "
                                                                ></span
                                                                ><span
                                                                    class="ml-1 text-sm"
                                                                    style="
                                                                        color: #595959;
                                                                    "
                                                                    >{{
                                                                        pcsInfoData?.errorStatus ||
                                                                        pcsInfoData.errorStatus ==
                                                                            0
                                                                            ? errorStatus[
                                                                                  pcsInfoData
                                                                                      .errorStatus
                                                                              ]
                                                                            : '-'
                                                                    }}</span
                                                                >
                                                            </li>
                                                            <li class="flex-1">
                                                                <span
                                                                    class="text-sm"
                                                                    >{{
                                                                        $t(
                                                                            'station_bingwangzhuangtai'
                                                                        )
                                                                    }}：</span
                                                                ><span
                                                                    class="ml-1 text-sm"
                                                                    >{{
                                                                        pcsInfoData?.onGridStatus ||
                                                                        pcsInfoData.onGridStatus ==
                                                                            0
                                                                            ? onGridStatus[
                                                                                  pcsInfoData
                                                                                      .onGridStatus
                                                                              ]
                                                                            : '-'
                                                                    }}</span
                                                                >
                                                            </li>

                                                            <li class="flex-1">
                                                                <span
                                                                    class="text-sm"
                                                                    >{{
                                                                        $t(
                                                                            'station_chongfangdianzhuangtai'
                                                                        )
                                                                    }}：</span
                                                                ><span
                                                                    class="ml-1 text-sm"
                                                                    >{{
                                                                        pcsInfoData?.chargeStatus ||
                                                                        pcsInfoData?.chargeStatus ==
                                                                            0
                                                                            ? batteryStatus[
                                                                                  pcsInfoData
                                                                                      .chargeStatus
                                                                              ]
                                                                            : '-'
                                                                    }}</span
                                                                >
                                                            </li>
                                                            <li class="flex-1">
                                                                <span
                                                                    class="text-sm"
                                                                    >{{
                                                                        $t(
                                                                            'staion_igbtwendu'
                                                                        )
                                                                    }}：{{
                                                                        pcsInfoData?.igbtTemperature ||
                                                                        pcsInfoData?.igbtTemperature ==
                                                                            0
                                                                            ? pcsInfoData?.igbtTemperature +
                                                                              ' °C'
                                                                            : '-'
                                                                    }}</span
                                                                >
                                                            </li>
                                                        </ul>
                                                    </template>
                                                    <template #content>
                                                        <div
                                                            class="content-desc"
                                                        >
                                                            <pcsInfoBox
                                                                :objectStyle="{
                                                                    height: '100%',
                                                                    padding:
                                                                        '16px 0',
                                                                }"
                                                                :data="
                                                                    pcsInfoData
                                                                "
                                                            />
                                                        </div>
                                                    </template>
                                                </TranslateBox>

                                                <TranslateBox
                                                    :objectStyle="{
                                                        height: '100%',
                                                    }"
                                                    v-if="showBox == 4"
                                                    :headers="false"
                                                >
                                                    <template #detail>
                                                    </template>
                                                    <template #content>
                                                        <div
                                                            class="content-desc"
                                                        >
                                                            <energyStorage
                                                                :objectStyle="{
                                                                    height: '100%',
                                                                    padding:
                                                                        '16px',
                                                                }"
                                                                :data="
                                                                    bmsMeterData
                                                                "
                                                            />
                                                        </div>
                                                    </template>
                                                </TranslateBox>

                                                <TranslateBox
                                                    :objectStyle="{
                                                        height: '100%',
                                                    }"
                                                    v-if="showBox == 3"
                                                    :headers="false"
                                                >
                                                    <template #content>
                                                        <div
                                                            class="content-desc"
                                                        >
                                                            <dynamicEnvironment
                                                                :objectStyle="{
                                                                    // padding: '10px',
                                                                    paddingRight: 0,
                                                                }"
                                                                :data="
                                                                    environmentSystem
                                                                "
                                                            />
                                                        </div>
                                                    </template>
                                                </TranslateBox>

                                                <TranslateBox
                                                    :objectStyle="{
                                                        height: '100%',
                                                    }"
                                                    v-if="showBox == 5"
                                                    :headers="false"
                                                >
                                                    <template #content>
                                                        <div
                                                            class="content-desc"
                                                        >
                                                            <TranslateList
                                                                @svgTab="svgTab"
                                                                :data="
                                                                    patteryPackList
                                                                "
                                                                :productModel="
                                                                    productModel
                                                                "
                                                                :containerNum="
                                                                    containerNums
                                                                "
                                                            ></TranslateList>
                                                        </div>
                                                    </template>
                                                </TranslateBox>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a-spin>
                            <div
                                class="w-full"
                                style="height: 403px"
                                v-if="deviceDataViewType == 'table'"
                            >
                                <div
                                    class="w-full flex justify-end items-center gap-x-3"
                                >
                                    <div class="flex items-center">
                                        <el-select
                                            v-model="deviceType"
                                            :placeholder="$t('Device Type')"
                                            style="width: 120px"
                                            class=""
                                            @change="onTypeChange"
                                        >
                                            <el-option
                                                v-for="item in deviceTypes"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>

                                    <div class="flex items-center device">
                                        <el-select
                                            v-model="showField"
                                            :placeholder="
                                                $t('station_zhanshiziduan')
                                            "
                                            style="width: 150px"
                                            class=""
                                            multiple
                                            :multiple-limit="30"
                                            collapse-tags
                                            collapse-tags-tooltip
                                            @change="onShowFieldChange"
                                        >
                                            <el-option
                                                v-for="item in showFields"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>

                                    <div class="flex items-center">
                                        <date-search
                                            :info="{
                                                periodOptions: [],
                                                datePickerType: 'day',
                                                minDate: cascaderStartDate,
                                                dayRangeLen: 2,
                                            }"
                                            v-model:dateSelect="rangeDate"
                                            @onChange="onDateChange"
                                        />
                                    </div>
                                </div>
                                <div
                                    class="table-tabs mt-3"
                                    style="height: 300px"
                                >
                                    <device-data
                                        v-model:tableData="otherTableData"
                                        :tableColumn="otherTableColumn"
                                        :loading="tableLoading"
                                    />
                                    <!-- 这个分页由于时间关系和数据在外面，所以先写外面了 -->
                                    <div class="flex justify-end mt-4">
                                        <el-pagination
                                            background
                                            layout="prev, pager, next"
                                            :total="pageTotal"
                                            v-model:current-page="
                                                pageInfo.current
                                            "
                                            :page-size="pageInfo.size"
                                            @change="pageChange"
                                            @current-change="
                                                handleCurrentChange
                                            "
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="p-4 bg-ff dark:bg-car-pie-border rounded-lg"
                            style="min-height: 600px"
                        >
                            <WorkOrder
                                :title="$t('status_yichang')"
                                type="alarm"
                                :getAlarmDataFlag="getAlarmDataFlag"
                            />
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="b">
                        <div
                            class="p-4 bg-ff dark:bg-car-pie-border rounded-lg"
                        >
                            <div class="flex justify-between items-center mb-3">
                                <div class="text-title dark:text-title-dark">
                                    {{ $t('station_shouyitongji') }}
                                </div>
                                <div class="flex justify-between">
                                    <date-search
                                        :info="{
                                            periodOptions: ['day', 'month'],
                                            datePickerType: 'minute',
                                            dayRangeLen: 30,
                                            defaultDayRangeLen: 7,
                                            minDate: moment(
                                                stationInfo.createTime
                                            ).format('YYYY-MM-DD'),
                                        }"
                                        @onChange="incomeDateSearchChange"
                                        v-model:dateSelect="incomeDateSelect"
                                    />

                                    <toggleView
                                        @change="onChangeProfitView"
                                        v-model:type="profitViewType"
                                        class="ml-4"
                                    />
                                    <el-popconfirm
                                        :title="
                                            $t('export_tips01')
                                                .replace(
                                                    's%',
                                                    incomeDateSelect?.startDate ||
                                                        incomeDateSelect?.startMonth
                                                )
                                                .replace(
                                                    's%',
                                                    incomeDateSelect?.endDate ||
                                                        incomeDateSelect?.endMonth
                                                )
                                        "
                                        @confirm="confirmExportProfit"
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                    >
                                        <template #reference>
                                            <export-button />
                                        </template>
                                    </el-popconfirm>
                                </div>
                            </div>
                            <!-- 393px -->
                            <div
                                class="w-full"
                                style="height: 600px"
                                id="incomeEcharts"
                                v-if="profitViewType == 'chart'"
                            ></div>
                            <div
                                class=""
                                style="height: 600px"
                                v-if="profitViewType == 'table'"
                            >
                                <charge-fee-data
                                    v-model:tableData="chargeFeeTableData"
                                />
                            </div>
                        </div>
                        <income-ai-model
                            v-if="1 > 2"
                            :data="{
                                stationId: stationInfo.id,
                                stationNo: stationInfo.stationNo,
                                stationName: stationInfo.stationName,
                                pendingData: pendingData,
                                responseData: responseData,
                                profitChartData: profitChartData,
                                openVpp: vppInfo.openVpp,
                                aiTrainStatus: vppInfo.aiTrainStatus,
                                stationInfo: stationInfo,
                            }"
                        />
                    </a-tab-pane>
                </a-tabs>
            </div>
            <a-modal
                v-model:visible="showAnalyze"
                width="1200px"
                :footer="null"
                :title="$t('device_dianxinfenxi')"
                class="modal-box"
                @cancel="onModalClose"
            >
                <div class="flex justify-between electricity bt-box">
                    <div class="flex-1 mr-1 rounded overflow-hidden">
                        <TranslateBox>
                            <template #detail>
                                <div class="p-2.5">
                                    {{ $t('station_cuneidianxinyachafenxi') }}
                                </div>
                            </template>
                            <template #content>
                                <div class="w-full flex flex-col bg-bt">
                                    <div class="flex justify-between">
                                        <div class="my-tab">
                                            <div
                                                :class="{
                                                    'tab-item': true,
                                                    'title-family': true,
                                                    'active-tab-item':
                                                        tabIndexOne == i,
                                                }"
                                                @click="voltageClick(i)"
                                                v-for="(item, i) in tabList"
                                                :key="i"
                                            >
                                                {{ item }}
                                            </div>
                                        </div>
                                        <div class="tab-select">
                                            <a-select
                                                :options="[
                                                    {
                                                        label: $t(
                                                            'common_jintian'
                                                        ),
                                                        value: '0',
                                                    },
                                                    {
                                                        label: $t('Last Week'),
                                                        value: '1',
                                                    },
                                                    {
                                                        label: $t(
                                                            'Last 30 Days'
                                                        ),
                                                        value: '2',
                                                    },
                                                ]"
                                                class="w-30"
                                                v-model:value="voltageTime"
                                                @change="voltageChange"
                                            />
                                        </div>
                                    </div>
                                    <a-spin :spinning="voltageLoading">
                                        <div
                                            id="voltage"
                                            class="voltage my-rounded-lg overflow-hidden"
                                        ></div>
                                    </a-spin>
                                </div>
                            </template>
                        </TranslateBox>
                    </div>
                    <div class="flex-1 ml-1 rounded overflow-hidden">
                        <TranslateBox>
                            <template #detail>
                                <div class="p-2.5">
                                    {{ $t('station_cuneidainxinwenchafenxi') }}
                                </div>
                            </template>
                            <template #content>
                                <div class="w-full flex flex-col bg-bt">
                                    <div class="flex justify-between">
                                        <div class="my-tab">
                                            <div
                                                :class="{
                                                    'tab-item': true,
                                                    'title-family': true,
                                                    'active-tab-item':
                                                        tabIndexTwo == i,
                                                }"
                                                v-for="(item, i) in tabList"
                                                :key="i"
                                                @click="electricaClick(i)"
                                            >
                                                {{ item }}
                                            </div>
                                        </div>
                                        <div class="tab-select">
                                            <a-select
                                                :options="[
                                                    {
                                                        label: $t(
                                                            'common_jintian'
                                                        ),
                                                        value: '0',
                                                    },
                                                    {
                                                        label: $t('Last Week'),
                                                        value: '1',
                                                    },
                                                    {
                                                        label: $t(
                                                            'Last 30 Days'
                                                        ),
                                                        value: '2',
                                                    },
                                                ]"
                                                class="w-30"
                                                v-model:value="electricalTime"
                                                @change="electricalChange"
                                            />
                                        </div>
                                    </div>
                                    <a-spin :spinning="electricLoading">
                                        <div
                                            id="electric"
                                            class="voltage my-rounded-lg overflow-hidden"
                                        ></div>
                                    </a-spin>
                                </div>
                            </template>
                        </TranslateBox>
                    </div>
                </div>
            </a-modal>
        </div>
    </a-spin>
</template>

<script>
import * as echarts from 'echarts'
// import apiDeviceMock from '@/api/deviceMock'
import {
    getChargeOption,
    lineData,
    getLineData,
    powerLineData,
    getPowerLineData,
    pieData,
    DateOptionsMap,
    chargeStatus,
    unitConversion,
    alternateUnits,
    updateEcharts,
    updateEchart,
    earningsOption,
    getEarningsOption,
    filterDate,
    isBetweenDate,
    runningStatus,
    onGridStatus,
    errorStatus,
    batteryStatus,
    dayAbs,
    timeOption,
    getTimeOption,
    someMax,
    roundNumFun,
    transformPrice,
    transformPrices,
    chargeOption24H,
    getChargeOption24H,
    getElectricityData,
} from './const'

import { getState, getSegmentTypeColor } from '@/common/util.js'
import { fullTimePeriod, segmentTypes } from '@/views/strategy/util.js'
import {
    ref,
    onMounted,
    reactive,
    nextTick,
    computed,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
} from 'vue'
import TranslateBox from './components/translateBox.vue'
// import Descriptions from './components/descriptions.vue'
import TranslateList from './components/translateList.vue'
import svgComments from './svgComments.vue'
import { useRoute, useRouter } from 'vue-router'
import apiService from '@/apiService/device'
import apiVpp from '@/apiService/vpp'
import api from '@/apiService/strategy'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import _round from 'lodash/round'
import { message, Modal } from 'ant-design-vue'
import bmsBox from './components/bmsBox.vue'
import pcsInfoBox from './components/pcsInfoBox.vue'
import energyStorage from './components/energyStorage.vue'
import dynamicEnvironment from './components/dynamicEnvironment.vue'
import Percentage from './components/percentage.vue'
import { useStore } from 'vuex'
import WorkingStatus from './detail/workingStatus.vue'
import WorkOrder from '@/views/operation/components/workOrder.vue'
import editInfo from './home/<USER>'
import RunningTrajectory from './charts/runningTrajectory.vue'
import AiOptimizeDrawer from './components/aiOptimizeDrawer.vue'
import IncomeAiModel from './components/incomeAiModel.vue'
import moment from 'moment'
import * as XLSX from 'xlsx' //引入 xlsx 库，将数据转换为 Excel 并下载
import DateSearch from '@/components/dateSearch.vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import toggleView from '@/components/toggle.vue'
import ChargeData from '@/views/role/components/chargeData.vue'
import ChargeFeeData from '@/views/role/components/chargeFeeData.vue'
import deviceData from '@/views/role/components/deviceData.vue'
import { useI18n } from 'vue-i18n'
import useTheme from '@/common/useTheme'
export default {
    name: 'deviceDetail',
    //
    components: {
        // Descriptions,
        TranslateBox,
        svgComments,
        bmsBox,
        pcsInfoBox,
        energyStorage,
        dynamicEnvironment,
        TranslateList,
        Percentage,
        WorkingStatus,
        // AlarmOverview,
        WorkOrder,
        editInfo,
        RunningTrajectory,
        AiOptimizeDrawer,
        IncomeAiModel,
        DateSearch,
        toggleView,
        ChargeData,
        ChargeFeeData,
        deviceData,
    },
    setup() {
        const { t, locale } = useI18n()
        const loading = ref(false)
        const propActive = ref(0)
        const svgTab = (e) => {
            propActive.value = e
            selectBattery.value = e - 3
        }

        const store = useStore()
        const isEmsControl = computed(() => {
            return store.state.user.userInfoData.permissions.includes(
                'emsControl'
            )
        })
        const route = useRoute()
        const customerDetail = ref(route.query)
        const tabCabinetActive = ref(void 0)
        const powerDate = ref([
            moment().format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD'),
        ])

        const router = useRouter()

        const tabList = [t('device_jichafenxi'), t('device_fangchafenxi')]

        const tabIndexOne = ref(0)
        const tabIndexTwo = ref(0)

        const chargeSelect = ref('1')
        const efficiencySelect = ref({
            periodType: 'day',
            startDate: moment().subtract(7, 'days').format('YYYY-MM-DD'),
            endDate: moment().subtract(1, 'days').format('YYYY-MM-DD'),
        })
        const powerRrend = ref('0')

        const voltageTime = ref('0')

        const electricalTime = ref('0')

        const pickerTime = ref()

        //1表示展示整个机柜数据
        const showBox = ref(1)

        const containerNums = ref('1')

        const spinning = ref(false)

        const electricLoading = ref(false)

        const voltageLoading = ref(false)

        const defaultImg =
            sessionStorage.getItem('stationPic') ||
            require('@/assets/device/defaultImg.png')

        //获取头部站点信息
        const stationInfo = reactive({
            alarmQuantity: void 0,
            avgTemperature: void 0,
            createTime: void 0,
            address: void 0,
            installedCapacity: void 0,
            installedPower: void 0,
            soc: void 0,
            soh: void 0,
            stationNo: void 0,
            status: void 0,
            stationName: undefined,
            stationPic: defaultImg,
            id: undefined,
        })

        //获取三个块数据
        const pieceOthenData = reactive({
            comparedCharge: 0,
            comparedDischarge: 0,
            comparedProfit: 0,
            currentMonthCharge: 0,
            currentMonthDischarge: 0,
            currentMonthProfit: 0,
            totalCharge: 0,
            totalDischarge: 0,
            totalProfit: 0,
            yesterdayCharge: 0,
            yesterdayDischarge: 0,
            yesterdayProfit: 0,
        })
        // 导出
        const exportExcel = (data, headers, fileName) => {
            // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
            const blob = new Blob([data], {
                type: headers['content-type'],
            })
            let dom = document.createElement('a')
            let url = window.URL.createObjectURL(blob)
            dom.href = url
            dom.download = decodeURI(fileName)
            dom.style.display = 'none'
            document.body.appendChild(dom)
            dom.click()
            dom.parentNode.removeChild(dom)
            window.URL.revokeObjectURL(url)
        }
        const getStatisticStationSummary = async () => {
            const { stationNo } = customerDetail.value
            const {
                data: { data, code },
            } = await apiService.getStatisticStationSummary({
                stationNo,
                stationType: 'energy_storage_cabinet',
            })
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    pieceOthenData[key] = data[key] || 0
                })
            } else {
                Object.keys(pieceOthenData).forEach((key) => {
                    pieceOthenData[key] = 0
                })
            }
        }

        //头部左边信息
        const getEchartsData = async () => {
            try {
                const { stationNo } = customerDetail.value
                const {
                    data: { data, code },
                } = await apiService.getStationInfoData({ stationNo })
                if (code === 0) {
                    Object.keys(data).forEach((key) => {
                        stationInfo[key] = data[key]
                        if (key == 'stationPic') {
                            stationInfo[key] = data[key]
                                ? data[key]
                                : defaultImg
                        }
                    })
                } else {
                    stationInfo.stationPic = defaultImg
                }
                const num = data.soc ? data.soc / 100 : 0
                pieDischargeInit(data.soc, num)
            } catch (error) {
                stationInfo.stationPic = defaultImg
            }
        }
        const excelData = ref()
        //充放电统计

        const chargeOptions = ref({})
        const chargeAndDischargeData = ref([])
        const chargeAndDischargeDataXData = ref()
        // 设置充放电统计图表
        const setChargeOptions = () => {
            //
            //
            const charge = []
            const chargeData = []
            const discharge = []
            const dischargeData = []
            chargeAndDischargeData.value.forEach((item) => {
                chargeData.push(item?.charge || 0)
                dischargeData.push(item?.discharge || 0)
                charge.push({
                    value: item?.charge || 0,
                    isBooan: false,
                })
                discharge.push({
                    value: item?.discharge || 0,
                    isBooan: false,
                })
            })

            const isBooan = someMax([...chargeData, ...dischargeData], 1000)
            nextTick(() => {
                excelData.value = chargeAndDischargeDataXData.value.map(
                    (item, index) => {
                        return {
                            date: item,
                            charge: chargeData[index],
                            discharge: dischargeData[index],
                            profit:
                                chargeAndDischargeData.value[index]?.profit ||
                                0,
                        }
                    }
                )
            })

            if (isBooan) {
                charge.forEach((item) => {
                    item.value = roundNumFun(item.value / 1000, 2)
                    item.isBooan = true
                })
                discharge.forEach((item) => {
                    item.value = roundNumFun(item.value / 1000, 2)
                    item.isBooan = true
                })
            }

            chargeOptions.value = _cloneDeep(getChargeOption())
            chargeOptions.value.xAxis.data = chargeAndDischargeDataXData.value
            chargeOptions.value.series[0].data = charge
            chargeOptions.value.series[1].data = discharge
            if (charge.length >= 25) {
                chargeOptions.value.series[0].barWidth = '12px'
                chargeOptions.value.series[1].barWidth = '12px'
            } else if (charge.length >= 12) {
                chargeOptions.value.series[0].barWidth = '30px'
                chargeOptions.value.series[1].barWidth = '30px'
            } else {
                chargeOptions.value.series[0].barWidth = '36px'
                chargeOptions.value.series[1].barWidth = '36px'
            }
            if (isBooan) {
                chargeOptions.value.yAxis.name = 'MWh'
            }
            updateEcharts('chargingStatisticsEchart', chargeOptions.value)
            updateEchart('chargingStatisticsEchart', chargeOptions.value)
        }
        const setChargeOptions24H = () => {
            options24H.value = _cloneDeep(getChargeOption24H())
            options24H.value.series[0].data = s1.value
            options24H.value.series[1].data = s2.value
            options24H.value.xAxis[0].data = x1.value
            updateEcharts('chargingStatisticsEchart', options24H.value)
        }
        // 获取充放电统计数据
        const getElectricityAndRevenueFun = async (params) => {
            if (chargeViewType.value == 'chart') {
                try {
                    const {
                        data: { data, code },
                    } = await apiService.getElectricityAndRevenue(params)
                    if (code === 0) {
                        chargeAndDischargeDataXData.value = data.map((item) => {
                            if (params.periodType == 'month') {
                                return dayjs(item.date).format('YYYY-MM')
                            }
                            return dayjs(item.date).format('MM/DD')
                        })
                        chargeAndDischargeData.value = Object.values(data)
                        setChargeOptions()
                    } else {
                        //
                    }
                } catch (error) {
                    //
                }
            }
            if (chargeViewType.value == 'table') {
                // 获取table数据
                const res1 = await apiVpp.statisticsDailyChargeAndProfitDetail({
                    ...params,
                })
                chargeTableData.value = res1.data.data
                chargeFeeTableData.value = res1.data.data
            }
        }
        const chargeViewType = ref('chart')
        const onChangeView = async (e) => {
            await dateSearchChange()
        }
        function timeToMinutes(time) {
            const [hours, minutes] = time.split(':').map(Number)
            return hours * 60 + minutes
        }

        // Function to merge the arrays
        function mergeArrays(arr, arr1) {
            if (!arr1.length) return arr
            return arr.map((time) => {
                const timeInMinutes = timeToMinutes(time)

                const segment = arr1.find(({ startTime, endTime }) => {
                    const start = timeToMinutes(startTime)
                    const end = timeToMinutes(endTime)
                    return timeInMinutes >= start && timeInMinutes <= end
                })
                return {
                    time,
                    segmentType: segment?.segmentType ?? null,
                    price: segment?.price ?? null,
                    salePrice: segment?.salePrice ?? null,
                }
            })
        }

        const segmentTypeByHour = ref([])
        const segmentTypeByHour1 = ref([])
        const chargeChartData = ref([])
        const chargeTableData = ref([])
        const options24H = ref()
        // 获取充放电小时纬度的数据
        const s1 = ref([])
        const s2 = ref([])
        const x1 = ref([])
        const statisticsStation24HourChargeData = async (params) => {
            const res = await apiService.statisticsStation24HourCharge(params)
            if (res.data.data) {
                s1.value = res.data.data
                    .map((item) => item.charge)
                    .flatMap((ite) => [undefined, ite < 3 ? 0 : ite])
                s1.value.push(undefined)
                s2.value = res.data.data
                    .map((item) => item.discharge)
                    .flatMap((ite) => [undefined, ite])
                s2.value.push(undefined)
                x1.value = res.data.data
                    .map(
                        (item) =>
                            String(item.hour).padStart(2, '0') +
                            ':00' +
                            '-' +
                            String(item.hour + 1).padStart(2, '0') +
                            ':00'
                    )
                    .flatMap((ite) => [ite.split(':')[0] + '', ite])
                x1.value.push('24')
                excelData.value = res.data.data.map((item) => {
                    return {
                        date: String(item.hour).padStart(2, '0') + ':00',
                        charge: item.charge < 3 ? 0 : item.charge,
                        discharge: item.discharge || 0,
                        profit: item.profit || 0,
                    }
                })
                chargeTableData.value = res.data.data.map((item) => {
                    return {
                        date: String(item.hour).padStart(2, '0') + ':00',
                        charge: item.charge || 0,
                        discharge: item.discharge || 0,
                        profit: item.profit || 0,
                    }
                })
                const chargeTotal = chargeTableData.value.reduce(
                    (total, item) => total + item.charge,
                    0
                )
                const dischargeTotal = chargeTableData.value.reduce(
                    (total, item) => total + item.discharge,
                    0
                )
                chargeTableData.value.push({
                    date: '总计',
                    charge: Math.round(chargeTotal * 100) / 100,
                    discharge: Math.round(dischargeTotal * 100) / 100,
                })
            } else {
                excelData.value = []
            }
            const res1 = await apiService.dayElectricPriceSegmentRecord({
                stationId: stationInfo.id,
                date: params.startDate,
            })
            function calculateTimeDifference(start, end) {
                const [startHour, startMinute] = start.split(':').map(Number)
                const [endHour, endMinute] = end.split(':').map(Number)
                const startTotalMinutes = startHour * 60 + startMinute
                const endTotalMinutes = endHour * 60 + endMinute
                return endTotalMinutes - startTotalMinutes
            }

            if (res1.data.data.length) {
                // 计算每个时间段的占比
                const result = res1.data.data.map((item) => {
                    const duration = calculateTimeDifference(
                        item.startTime,
                        item.endTime
                    )
                    const percentage = (duration / 1440).toFixed(5) * 100 + '%'
                    return {
                        ...item,
                        colors: { ...getSegmentTypeColor(item.segmentType) },
                        percentage: percentage,
                        label: segmentTypes.find(
                            (type) => type.value == item.segmentType
                        ).label,
                        text: segmentTypes.find(
                            (type) => type.value == item.segmentType
                        ).text,
                    }
                })

                segmentTypeByHour1.value = result
            } else {
                segmentTypeByHour1.value = []
            }

            const mergedArray = mergeArrays(fullTimePeriod, res1.data.data)
            segmentTypeByHour.value = mergedArray
            setChargeOptions24H()
        }
        const dateSelect = ref({
            periodType: 'day',
            startDate: moment().subtract(7, 'days').format('YYYY-MM-DD'),
            endDate: moment().format('YYYY-MM-DD'),
        })
        const dateSearchChange = async (params) => {
            //
            const { stationNo } = customerDetail.value
            if (dateSelect.value && dateSelect.value.periodType == 'hour') {
                await statisticsStation24HourChargeData(
                    {
                        ...dateSelect.value,
                        stationNo,
                        stationType: 'energy_storage_cabinet',
                    },
                    'chargingStatisticsEchart',
                    getChargeOption24H()
                )
            } else {
                await getElectricityAndRevenueFun({
                    ...dateSelect.value,
                    stationNo,
                    stationType: 'energy_storage_cabinet',
                })
            }
        }
        const setIncomeChartOptions = () => {
            const options = _cloneDeep(getEarningsOption())
            if (demandProfitIsBoolean.value) {
                options.yAxis.name =
                    locale.value == 'zh'
                        ? t('station_shouyi') + '/' + t('common_wanyuan')
                        : t('station_shouyi')
            } else {
                options.yAxis.name =
                    locale.value == 'zh'
                        ? t('station_shouyi') + '/' + t('common_yuan')
                        : t('station_shouyi')
            }
            options.xAxis.data = incomeDate.value
            options.series[0].data = incomeProfit.value
            // options.grid.left = locale.value == 'zh' ? '40px' : '60px'
            const isAi = vppInfo.value.openVppDemand
            if (isAi) {
                options.series.push({
                    stack: 'Ad',
                    data: demandProfit.value,
                    type: 'bar',
                    barWidth: `20px`, // xxx
                    color: '#6FBECE',
                    name: '需求响应收益',
                })
            }
            if (incomeProfit.value.length >= 25) {
                options.series[0].barWidth = '12px'
                if (isAi) {
                    options.series[1].barWidth = '12px'
                }
            } else if (incomeProfit.value.length >= 12) {
                options.series[0].barWidth = '30px'
                if (isAi) {
                    options.series[1].barWidth = '30px'
                }
            } else {
                options.series[0].barWidth = '35px'
                if (isAi) {
                    options.series[1].barWidth = '35px'
                }
                //xxxx
            }
            // if(incomeProfit.value)
            updateEcharts('incomeEcharts', options)
        }

        //收益
        const syData = ref()
        const incomeDate = ref([])
        const incomeProfit = ref([])
        const incomeProfitData = ref([])
        const demandProfitData = ref()
        const demandProfit = ref()
        const demandProfitIsBoolean = ref(false)
        const getIncome = async (params, id, option) => {
            if (profitViewType.value == 'chart') {
                try {
                    const {
                        data: { data, code },
                    } = await apiService.getElectricityAndRevenue(params)
                    if (code === 0) {
                        syData.value = data
                        incomeDate.value = data.map((item) => {
                            if (params.periodType == 'month') {
                                return dayjs(item.date).format('YYYY-MM')
                            }
                            return dayjs(item.date).format('MM/DD')
                        })
                        const dataList = Object.values(data)
                        incomeProfit.value = []
                        incomeProfitData.value = []
                        demandProfitData.value = dataList.map((item) => {
                            return item?.demandProfit
                        })
                        demandProfit.value = []
                        dataList.forEach((item) => {
                            incomeProfitData.value.push(
                                item?.arbitrageProfit || 0
                            )
                            incomeProfit.value.push({
                                value: item?.arbitrageProfit || 0,
                                isBooan: false,
                            })
                            demandProfit.value.push({
                                value: item?.demandProfit || 0,
                                isBooan: false,
                            })
                        })

                        demandProfitIsBoolean.value =
                            someMax(incomeProfitData.value, 10000) ||
                            someMax(demandProfitData.value, 10000)
                        if (demandProfitIsBoolean.value) {
                            incomeProfit.value.forEach((item) => {
                                item.value = roundNumFun(item.value / 10000, 2)
                                item.isBooan = true
                            })
                            demandProfit.value.forEach((item) => {
                                item.value = roundNumFun(item.value / 10000, 2)
                                item.isBooan = true
                            })
                        }
                        //
                        setIncomeChartOptions()
                    } else {
                        updateEcharts(id, option)
                    }
                } catch (error) {
                    updateEcharts(id, option)
                }
            }
            if (profitViewType.value == 'table') {
                const res = await apiVpp.statisticsDailyChargeAndProfitDetail({
                    ...params,
                })
                chargeFeeTableData.value = res.data.data
            }
        }

        const disabledDate = (current) => {
            return (
                (current && current > dayjs().endOf('day')) ||
                current < dayjs().add(-365, 'day').startOf('day')
            )
        }
        const setPowerTrendOptions = () => {
            //
            const options = _cloneDeep(getPowerLineData())
            if ((PowerTrendMin1.value === 0) & (PowerTrendMin2.value === 0)) {
                options.xAxis[0].axisLine.lineStyle.width = 1
            } else {
                options.xAxis[0].axisLine.lineStyle.width = 0
            }
            // 隐藏下面的代码，可以使左侧Y轴最大最小自适应，但是会造成某些情况下左右y轴的分割虚线不对齐
            // options.yAxis[0].min = min1
            // options.yAxis[0].max = max1
            // options.yAxis[0].interval = (max1 - min1) / 5

            // options.yAxis[1].min = min2
            // options.yAxis[1].max = max2
            // options.yAxis[1].interval = (max2 - min2) / 5

            options.series[0].data = bmsPower.value
            options.series[1].data = gridPower.value
            options.series[2].data = PowerTrendSoc.value
            options.xAxis[0].data = PowerTrendDate.value
            updateEcharts('powerLine', options)
        }
        //功率趋势分析
        const glData = ref()
        const bmsPower = ref([])
        const gridPower = ref([])
        const PowerTrendSoc = ref([])
        const PowerTrendDate = ref([])
        const PowerTrendMin1 = ref()
        const PowerTrendMin2 = ref()
        const getStationPowerTrendData = async (params, id, option) => {
            try {
                spinning.value = true
                const {
                    data: { data, code },
                } = await apiService.getStationPowerTrend(params)
                if (code === 0) {
                    glData.value = data
                    PowerTrendDate.value = Object.keys(data)
                    PowerTrendSoc.value = Object.values(data).map(
                        (item) => item.soc
                    )
                    bmsPower.value = Object.values(data).map(
                        (item) => item.bmsPower
                    )
                    gridPower.value = Object.values(data).map(
                        (item) => item.gridPower
                    )
                    PowerTrendMin1.value = Math.min.apply(null, [
                        ...bmsPower.value,
                        ...gridPower.value,
                    ])
                        ? Math.floor(
                              Math.min.apply(null, [
                                  ...bmsPower.value,
                                  ...gridPower.value,
                              ]) / 5
                          ) * 5
                        : 0

                    PowerTrendMin2.value = Math.min.apply(null, [
                        ...PowerTrendSoc.value,
                    ])
                        ? Math.floor(
                              Math.min.apply(null, [...PowerTrendSoc.value]) / 5
                          ) * 5
                        : 0

                    let isBooan = null
                    if (!powerRrend.value) {
                        const [startDate, endDate] = powerDate.value
                        isBooan =
                            new Date(startDate).getTime() -
                                new Date(endDate).getTime() ==
                            0
                                ? true
                                : false
                    } else {
                        const [startDate, endDate] = filterDate(
                            powerRrend.value
                        )
                        isBooan =
                            new Date(startDate).getTime() -
                                new Date(endDate).getTime() ==
                            0
                                ? true
                                : false
                    }

                    if (isBooan) {
                        PowerTrendDate.value = PowerTrendDate.value.map(
                            (item) => {
                                //苹果浏览器不能识别只有月日的数据转换 所以要自己拼接年
                                const years = dayjs().format('YYYY')
                                return dayjs(`${years}-${item}`).format('HH:mm')
                            }
                        )
                    }
                    setPowerTrendOptions()
                } else {
                    updateEcharts(id, option)
                }
                spinning.value = false
            } catch (error) {
                spinning.value = false
                updateEcharts(id, option)
            }
        }

        const stationPowerTrendDataInit = () => {
            let params = {}
            const { stationNo } = customerDetail.value
            if (!powerRrend.value) {
                const [startDate, endDate] = powerDate.value
                if (!startDate || !endDate) {
                    message.error(t('placeholder_qingxuanzeriqi'))
                    return
                }
                params = {
                    startDate: dayjs(startDate).format('YYYY-MM-DD'),
                    endDate: dayjs(endDate).format('YYYY-MM-DD'),
                    stationNo,
                }
            } else {
                const [startDate, endDate] = filterDate(powerRrend.value)
                params = {
                    startDate: startDate,
                    endDate: endDate,
                    stationNo,
                }
            }
            getStationPowerTrendData(params, 'powerLine', getPowerLineData())
        }
        const powerDateSelect = ref()
        const powerDateChange = () => {
            powerRrend.value = void 0
            const [startDate, endDate] = powerDate.value
            if (!startDate || !endDate) {
                message.error(t('placeholder_qingxuanzeriqi'))
                return
            }
            const isBooan = isBetweenDate(
                new Date(startDate),
                new Date(endDate),
                7
            )
            if (isBooan) {
                stationPowerTrendDataInit()
            } else {
                message.error(t('The maximum date range cannot exceed 7 days'))
            }
        }
        const dates = ref([])

        const onCalendarChange = (val) => {
            dates.value = val
        }
        const onVisibleChange = (e) => {
            dates.value = null
        }
        const disabledDates = (current) => {
            if (!dates.value || dates.value.length === 0) {
                return false
            }
            const diffDate = moment(current).diff(dates.value[0], 'days')
            return Math.abs(diffDate) > 6
        }

        //第一个图表
        const pieDischargeInit = (soc, value) => {
            const option = _cloneDeep(pieData)
            soc = soc ? soc : 0
            if (soc < 10) {
                option.series[0].axisLine.lineStyle.color[0] = [
                    value,
                    '#FF4D4F',
                ]
                option.series[1].itemStyle.color = '#FF4D4F'
            } else if (soc >= 10 && soc < 30) {
                option.series[0].axisLine.lineStyle.color[0] = [
                    value,
                    '#FD750B',
                ]
                option.series[1].itemStyle.color = '#FD750B '
            } else {
                option.series[0].axisLine.lineStyle.color[0] = [
                    value,
                    '#3EDACD',
                ]
                option.series[1].itemStyle.color = '#3EDACD'
            }
            if (!soc) {
                option.series[1].amplitude = 0
                option.series[1].waveAnimation = 0
            }
            option.series[1].data = [value, value]
            option.series[1].label.formatter = `${soc}%`
            option.series[0].data[0].detail.formatter = t(
                'station_shengyudianliang'
            )
            const chartDischargeDom = document.getElementById('chartDischarge')
            chartDischargeDom && echarts.dispose(chartDischargeDom)
            echarts.init(chartDischargeDom).setOption(option)
        }

        const errorEcarts = () => {
            const electricityOption = _cloneDeep(getElectricityData())
            electricityOption.yAxis.name = t('unit_danwei') + '/°C'
            updateEcharts('voltage', getElectricityData())
            updateEcharts('electric', electricityOption)
        }
        //获取机柜tab
        const containerList = ref([])
        const productModel = ref('SE215')
        const getContainerList = async () => {
            const { stationNo } = customerDetail.value
            const {
                data: { data, code },
            } = await apiService.getContainerList({ stationNo })
            if (code === 0) {
                containerList.value = data.map((item) => {
                    return {
                        ...item,
                        title:
                            t('station_jigui') + item.containerNo.substring(3),
                    }
                })
                tabCabinetActive.value =
                    containerList.value[0]?.containerNo || void 0
                productModel.value = containerList.value[0]?.productModel
                if (tabCabinetActive.value) {
                    const [startDate, endDate] = filterDate(voltageTime.value)
                    const [start, end] = filterDate(electricalTime.value)
                    nextTick(() => {
                        getBmsInfoData({
                            containerNo: tabCabinetActive.value,
                            stationNo,
                        })
                    })
                }
            }
        }

        //电池柜数据
        const bmsInfoData = reactive({
            runStatus: void 0,
            chargeStatus: void 0,
            soh: void 0,
            soc: void 0,
            totalVoltage: void 0,
            totalElectricity: void 0,
            chargeCapacity: void 0,
            dischargeCapacity: void 0,
            insulationValue: void 0,
            avgTemperature: void 0,
            avgVoltage: void 0,
            maxTemperatureId: void 0,
            maxTemperature: void 0,
            minTemperatureId: void 0,
            minTemperature: void 0,
            maxVoltageId: void 0,
            maxVoltage: void 0,
            minVoltageId: void 0,
            minVoltage: void 0,
        })
        const getBmsInfoData = async (params) => {
            cabineLoading.value = true
            const {
                data: { data, code },
            } = await apiService.getBmsInfo(params)
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    bmsInfoData[key] = data[key]
                })
            }
            cabineLoading.value = false
        }

        //获取变流器信息
        const pcsInfoData = reactive({
            errorStatus: void 0,
            onGridStatus: void 0,
            chargeStatus: void 0,
            igbtTemperature: void 0,
            pa: void 0,
            pb: void 0,
            pc: void 0,
            sa: void 0,
            sb: void 0,
            sc: void 0,
            qa: void 0,
            qb: void 0,
            qc: void 0,
            p: void 0,
            s: void 0,
            q: void 0,
            ua: void 0,
            ub: void 0,
            uc: void 0,
            ia: void 0,
            ib: void 0,
            ic: void 0,
            df: void 0,
            containerNo: void 0,
        })

        const gePcstInfoData = async (params) => {
            cabineLoading.value = true
            const {
                data: { data, code },
            } = await apiService.getPcsInfo(params)
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    pcsInfoData[key] = data[key]
                })
            }
            cabineLoading.value = false
        }

        //获取储能电表数据
        const bmsMeterData = reactive({
            epp: void 0,
            epn: void 0,
            p: void 0,
            pt: void 0,
            ct: void 0,
        })
        const getBmsMeterData = async (params) => {
            cabineLoading.value = true
            const {
                data: { data, code },
            } = await apiService.getBmsMeter(params)
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    bmsMeterData[key] = data[key]
                })
            }
            cabineLoading.value = false
        }

        //柜动环管理
        const environmentSystem = ref({})
        const getDynamicEnvironmentSystem = async (params) => {
            cabineLoading.value = true
            const {
                data: { data, code },
            } = await apiService.getDynamicEnvironmentSystem(params)
            if (code === 0) {
                environmentSystem.value = data
            }
            cabineLoading.value = false
        }

        //电芯
        const patteryPackList = ref([])
        const getBatteryPackList = async (params, containerNo = null) => {
            const {
                data: { data, code },
            } = await apiService.getBatteryPackList(params)
            if (code === 0) {
                patteryPackList.value = data || []
            }
        }

        const voltageRange = ref([])
        const voltageVariance = ref([])
        const rangeAndVarianceDate = ref([])
        const getBatteryVoltageRangeAndVariance = async (
            params,
            id,
            option
        ) => {
            try {
                voltageLoading.value = true
                const {
                    data: { data, code },
                } = await apiService.getBatteryVoltageRangeAndVariance(params)
                voltageLoading.value = false
                if (code === 0) {
                    rangeAndVarianceDate.value = Object.keys(data)
                    voltageRange.value = Object.values(data).map((item) => {
                        return item?.voltageRange || 0
                    })

                    voltageVariance.value = Object.values(data).map((item) => {
                        return item?.voltageVariance || 0
                    })
                    const options = _cloneDeep(option)
                    options.xAxis.data = rangeAndVarianceDate.value
                    if (tabIndexOne.value == 0) {
                        options.series[0].data = voltageRange.value
                    } else {
                        options.series[0].data = voltageVariance.value
                    }

                    updateEcharts(id, options)
                } else {
                    updateEcharts(id, option)
                }
            } catch (error) {
                voltageLoading.value = false
                updateEcharts(id, option)
            }
        }

        const tempRange = ref([])
        const tempVariance = ref([])
        const tempDateAll = ref([])
        const getBatteryTempRangeAndVariance = async (params, id, option) => {
            try {
                electricLoading.value = true
                const {
                    data: { data, code },
                } = await apiService.getBatteryTempRangeAndVariance(params)
                electricLoading.value = false
                if (code === 0) {
                    tempDateAll.value = Object.keys(data)
                    tempRange.value = Object.values(data).map((item) => {
                        return item?.tempRange || 0
                    })
                    tempVariance.value = Object.values(data).map((item) => {
                        return item?.tempVariance || 0
                    })
                    const options = _cloneDeep(option)
                    options.yAxis.name = t('unit_danwei') + '/°C'
                    options.xAxis.data = tempDateAll.value
                    if (tabIndexTwo.value == 0) {
                        options.series[0].data = tempRange.value
                    } else {
                        options.series[0].data = tempVariance.value
                    }

                    updateEcharts(id, options)
                } else {
                    updateEcharts(id, option)
                }
            } catch (error) {
                electricLoading.value = false
                updateEcharts(id, option)
            }
        }

        const clickObj = ref({ showDetailBox: 1 })
        const svgClick = (obj) => {
            const { showDetailBox, containerNo } = obj
            clickObj.value = obj
            showBox.value = showDetailBox
            const { stationNo } = customerDetail.value
            // voltageTime.value = '1'
            // electricalTime.value = '1'
            tabIndexOne.value = 0
            tabIndexTwo.value = 0
            if (!tabCabinetActive.value) {
                if (showDetailBox == 1) {
                    errorEcarts()
                }

                return
            }
            if (showDetailBox == 1 || showDetailBox == 99) {
                showBox.value = 1
                nextTick(() => {
                    getBmsInfoData({
                        containerNo: tabCabinetActive.value,
                        stationNo,
                    })
                })
            }

            if (showDetailBox == 2) {
                gePcstInfoData({
                    containerNo: tabCabinetActive.value,
                    stationNo,
                })
            }

            if (showDetailBox == 4) {
                getBmsMeterData({
                    containerNo: tabCabinetActive.value,
                    stationNo,
                })
            }

            if (showDetailBox == 3) {
                getDynamicEnvironmentSystem({
                    containerNo: tabCabinetActive.value,
                    stationNo,
                })
            }

            if (showDetailBox == 5) {
                containerNums.value = containerNo
                propActive.value = containerNo + 3
                getBatteryPackList(
                    {
                        stationNo,
                        containerNo: tabCabinetActive.value,
                    },
                    containerNo
                )
            }
        }
        const selectBox = ref(1),
            selectBattery = ref(null)
        const DeviceClick = (obj) => {
            const { showDetailBox, containerNo } = obj
            selectBox.value = showDetailBox
            selectBattery.value = containerNo
            svgClick(obj)
        }

        const tabChange = () => {
            svgClickAllData(true)
            let index = containerList.value.findIndex(
                (item) => item.containerNo == tabCabinetActive.value
            )
            productModel.value = containerList.value[index]?.productModel
        }
        const cabineLoading = ref(false)
        const svgClickAllData = async (refresh) => {
            const { containerNo } = clickObj.value
            let showDetailBox = clickObj.value.showDetailBox
            if (refresh) {
                showDetailBox = 1
                showBox.value = 1
                propActive.value = 10
            }
            const { stationNo } = customerDetail.value

            if (showDetailBox == 1) {
                const [startDate, endDate] = filterDate(voltageTime.value)
                const [start, end] = filterDate(electricalTime.value)
                nextTick(() => {
                    getBmsInfoData({
                        containerNo: tabCabinetActive.value,
                        stationNo,
                    })
                })
            }

            if (showDetailBox == 2) {
                gePcstInfoData({
                    containerNo: tabCabinetActive.value,
                    stationNo,
                })
            }

            if (showDetailBox == 4) {
                getBmsMeterData({
                    containerNo: tabCabinetActive.value,
                    stationNo,
                })
            }

            if (showDetailBox == 3) {
                getDynamicEnvironmentSystem({
                    containerNo: tabCabinetActive.value,
                    stationNo,
                })
            }

            if (showDetailBox == 5) {
                propActive.value = containerNo + 3
                containerNums.value = containerNo
                getBatteryPackList(
                    {
                        stationNo,
                        containerNo: tabCabinetActive.value,
                    },
                    containerNo
                )
            }
            cabineLoading.value = false
        }

        const loadingData = async () => {
            if (cabineLoading.value) return
            cabineLoading.value = true
            if (deviceDataViewType.value == 'chart') {
                svgClickAllData(true)
            }
            if (deviceDataViewType.value == 'table') {
                await onSearchOther()
            }
            cabineLoading.value = false
        }

        const voltageClick = (index) => {
            tabIndexOne.value = index
            const option = _cloneDeep(getElectricityData())
            option.xAxis.data = rangeAndVarianceDate.value || []
            if (index == 0) {
                option.series[0].data = voltageRange.value || []
            } else {
                option.series[0].data = voltageVariance.value || []
            }

            updateEcharts('voltage', option)
        }

        const electricaClick = (index) => {
            tabIndexTwo.value = index
            const option = _cloneDeep(getElectricityData())
            option.xAxis.data = tempDateAll.value || []
            option.yAxis.name = t('unit_danwei') + '/°C'
            if (index == 0) {
                option.series[0].data = tempRange.value || []
            } else {
                option.series[0].data = tempVariance.value || []
            }

            updateEcharts('electric', option)
        }

        const initDom = () => {
            const options = _cloneDeep(getTimeOption())
            options.xAxis[0].data = time24H.value
            options.xAxis[1].data = time24H.value
            options.series[0].data = s124H.value
            options.series[1].data = s224H.value
            const demo = document.getElementById('demo')
            demo && echarts.dispose(demo)
            const setOption = echarts.init(demo)
            setOption && setOption.setOption(options)
        }

        // 1
        const voltageChange = () => {
            const { stationNo } = customerDetail.value
            const [startDate, endDate] = filterDate(voltageTime.value)
            const deviceSn = containerList.value.find((item) => {
                return item.containerNo == tabCabinetActive.value
            }).deviceSn
            getBatteryVoltageRangeAndVariance(
                {
                    stationNo,
                    deviceSn: deviceSn,
                    startDate,
                    endDate,
                },
                'voltage',
                getElectricityData()
            )
        }

        const electricalChange = () => {
            const { stationNo } = customerDetail.value
            const [start, end] = filterDate(electricalTime.value)
            const deviceSn = containerList.value.find((item) => {
                return item.containerNo == tabCabinetActive.value
            }).deviceSn
            getBatteryTempRangeAndVariance(
                {
                    stationNo,
                    deviceSn: deviceSn,
                    startDate: start,
                    endDate: end,
                },
                'electric',
                getElectricityData()
            )
        }

        const goBlack = (url) => {
            if (!url) {
                proxy.$message.error(t('URL address not configured yet'))
                return
            }
            window.open(url, '_blank')
        }
        // 获取24小时电量变化图
        const time24H = ref([])
        const s124H = ref([])
        const s224H = ref([])
        const getfullDayData = async (params) => {
            const {
                data: { data, code },
            } = await apiService.statisticsSocAndWorkStatusByDay(params)
            if (code === 0) {
                const colors = [
                    'rgba(0,0,0,0)',
                    'rgba(253, 117, 11, 0.1)',
                    'rgba(30, 204, 153, 0.1)',
                ]
                time24H.value = data.map((item) => {
                    return dayjs(item.time).format('HH:mm')
                })
                time24H.value.push('24:00')
                s124H.value = data.map((item) => item.soc)
                s124H.value.push(0)
                s224H.value = data.map((item) => {
                    return {
                        value: 100,
                        itemStyle: { color: colors[item.batteryStatus] },
                    }
                })

                // options.series[0].markArea.data = markData
                initDom()
            }
        }
        // 切换电量日期选择框
        const pickChange = async (params) => {
            const { stationNo } = customerDetail.value
            //
            await getfullDayData({
                stationNo,
                day: pickerTime.value.startDate,
            })
        }
        const setEfficiencyOptions = () => {
            const options = _cloneDeep(getLineData())
            if (
                dischargeAchievingRate.value.length == 0 &&
                comprehensiveEfficiency.value.length == 0
            ) {
                options.yAxis[0].min = 0
                options.yAxis[0].max = 100
            }

            if (
                dischargeAchievingRate.value.length > 0 &&
                comprehensiveEfficiency.value.length > 0
            ) {
                const arr = [
                    ...dischargeAchievingRate.value,
                    ...comprehensiveEfficiency.value,
                ]
                const isBoolean = arr.every((item) => item == 0)
                if (isBoolean) {
                    options.yAxis[0].min = 0
                    options.yAxis[0].max = 100
                }
            }
            options.series[0].data = dischargeAchievingRate.value
            options.series[1].data = comprehensiveEfficiency.value
            options.xAxis[0].data = efficiencyDate.value
            updateEcharts('lineCharts', options)
        }
        //系统效率
        const xlData = ref([])
        const dischargeAchievingRate = ref([])
        const comprehensiveEfficiency = ref([])
        const efficiencyDate = ref([])
        const systemEfficiency = async (params, option) => {
            const {
                data: { data, code },
            } = await apiService.getDischargeAchievingRate(params)
            if (code === 0) {
                efficiencyDate.value = Object.keys(data).map((item) => {
                    return dayjs(item).format('MM/DD')
                })
                xlData.value = data
                const list = Object.values(data)
                dischargeAchievingRate.value = list.map(
                    (item) => item.dischargeAchievingRate
                )
                comprehensiveEfficiency.value = list.map(
                    (item) => item.comprehensiveEfficiency
                )
                setEfficiencyOptions()
            } else {
                updateEcharts('lineCharts', option)
            }
        }

        // 切换系统效率日期选择
        const efficiencyChange = async (data) => {
            const { stationNo } = customerDetail.value
            let params = { ...efficiencySelect.value }
            systemEfficiency({ ...params, stationNo }, getLineData())
        }
        // 获取收告警统计
        const alarmData = ref({
            totalQuantity: undefined,
            processingQuantity: undefined,
            todayQuantity: undefined,
            sevenDayQuantity: undefined,
        })
        const getStatisticalCard = async () => {
            const { supplierId, stationNo } = customerDetail.value
            let res = await apiService.getWorkOrderStatisticalCard({
                supplierId: supplierId,
                stationNo: stationNo,
                stationType: 'energy_storage_cabinet',
            })
            alarmData.value = res.data.data
        }
        // tabs2
        const loadedPowerData = ref(false)
        const vppInfo = ref({})
        const completeVpp = computed(() => {
            if (!vppInfo.value.openVppTime) return false
            const fixedDate = new Date(vppInfo.value.openVppTime)
            const currentDate = new Date()
            const daysDifference =
                (currentDate - fixedDate) / (1000 * 60 * 60 * 24)
            return daysDifference > 15
        })
        const dayDiff = ref()
        const getVppInfo = async () => {
            let params = {
                stationId: stationInfo.id,
            }
            const res = await apiVpp.getVppStationInfo(params)
            vppInfo.value = res.data.data
            switchVal.value = res.data.data.openVppDemand || 0
            // vppInfo.value.openVpp = 1 // 用于测试是否开启ai
            // vppInfo.value.openVppTime = new Date('2024-07-17') // 用于测试时间，不用于正式
            // 计算剩余天数
            if (!vppInfo.value.openVppTime) {
                dayDiff.value = 15
            } else {
                const timeDiff =
                    new Date(dayjs(res.data.data.openVppTime)) -
                    new Date(dayjs())

                dayDiff.value = 15 + Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
            }
        }
        const RealStrategyAndAiRecommendStrategy = ref({})
        const getRealStrategyAndAiRecommendStrategy = async () => {
            const params = {
                stationId: stationInfo.id,
                runDt: dayjs().format('YYYY-MM-DD'),
            }
            const res = await apiVpp.getRealStrategyAndAiRecommendStrategy(
                params
            )
            RealStrategyAndAiRecommendStrategy.value = res.data.data
        }
        const emsInfo = ref({})
        const getStationEmsBasicInfo = async () => {
            let params = {
                stationId: stationInfo.id,
            }
            let res = await apiVpp.getStationEmsBasicInfo(params)
            emsInfo.value = res.data.data
        }
        const responseInfo = ref({
            settlementCount: 0,
            totalNgy: 0,
            totalAmount: 0,
        })
        const getVppDemandTotalSettlement = async () => {
            const res = await apiVpp.getVppDemandTotalSettlement({
                stationId: stationInfo.id,
            })
            responseInfo.value = res.data.data
        }
        const segmentTypeByHourWidth = ref('100%')
        const transformers = ref([])
        const getStationStaticInfo = async () => {
            const { stationNo } = customerDetail.value
            let res = await apiService.getStationStaticInfo({ stationNo })
            deviceInfo.value = res.data.data
            transformers.value = res.data.data.transformers[0]
        }
        const getBottomData = async () => {
            var divToTopHeight = document
                .getElementById('power')
                ?.getBoundingClientRect().top
            // 获取屏幕高度
            var windowHeight = document.documentElement.clientHeight
            if (
                divToTopHeight - windowHeight < 100 &&
                !loadedPowerData.value &&
                activeKey.value === '1'
            ) {
                loadedPowerData.value = true
                stationPowerTrendDataInit()
                getAlarmDataFlag.value = false
                setTimeout(() => {
                    getAlarmDataFlag.value = true
                }, 100)
            }
        }
        const containerRef = ref(null)

        const fetchData = async () => {
            localStorage.setItem('activeSystem', 'device')
            loading.value = true
            // 获取 充放电统计数据
            // await getChargeData()
            await getStatisticStationSummary()
            await getStatisticalCard()
            await getEchartsData()
            await dateSearchChange()
            const [startDate, endDate] = filterDate(chargeSelect.value)
            const { stationNo } = customerDetail.value
            // 24小时电量变化
            await pickChange()
            await getContainerList()
            await getStationStaticInfo()
            document.addEventListener('scroll', getBottomData)
            //
            // 设置segmentTypeByHourId的宽度和 id:widthBox一样
            nextTick(() => {
                const widthBox = document.getElementById('widthBox')
                if (widthBox) {
                    segmentTypeByHourWidth.value = widthBox.offsetWidth + 'px'
                }
            })
            // 系统效率表
            await efficiencyChange()

            loading.value = false
            // await getVppInfo()
            await getStationEmsBasicInfo() // 获取ems基础信息
            if (!vppInfo.value.openVpp) {
                return
            } else {
                // await getRealStrategyAndAiRecommendStrategy()   // xxxx
                // await getVppDemandTotalSettlement()  // xxxx
            }
        }
        onMounted(async () => {
            await fetchData()
        })
        const updateData = async () => {
            // infoVisible.value = false
            loadedPowerData.value = false
            document.removeEventListener('scroll', getBottomData)
            await fetchData()
        }
        // 页面销毁前
        onBeforeUnmount(() => {
            const demo = document.getElementById('demo')
            demo && echarts.dispose(demo)
            const chartDischargeDom = document.getElementById('chartDischarge')
            chartDischargeDom && echarts.dispose(chartDischargeDom)
            document.removeEventListener('scroll', getBottomData)
        })

        const goRouter = () => {
            // if (window.history.state?.back) {
            //     router.go(-1)
            // } else {
            //     router.replace('/device')
            // }
            router.replace('/device')
        }

        //
        const activeKey = ref('1')

        // 收益

        // 收益日期切换

        // 收益日期选择
        const incomeDateSelect = ref()
        const incomeDateSearchChange = async (params) => {
            await getIncome(
                {
                    ...incomeDateSelect.value,
                    stationNo: customerDetail.value.stationNo,
                    stationType: 'energy_storage_cabinet',
                },
                'incomeEcharts',
                getEarningsOption()
            )
        }

        const tabsList = ref(['1'])
        // const
        const pendingData = ref([])
        const responseData = ref([])
        const getVppDemandRecords = async () => {
            const res0 = await apiVpp.getVppDemandRecords({
                stationId: stationInfo.id,
                status: 0,
            })
            pendingData.value = res0.data.data || []
            const res1 = await apiVpp.getVppDemandRecords({
                stationId: stationInfo.id,
                status: 1,
            })
            responseData.value = res1.data.data || []
        }
        const profitChartData = ref([])
        const getProfitChartData = async () => {
            let params = {
                stationNo: stationInfo.stationNo,
                startMonth: moment().subtract(6, 'months').format('YYYY-MM'),
                endMonth: moment().format('YYYY-MM'),
                periodType: 'month',
            }
            const res = await apiVpp.statisticsStationDateChargeAndNetProfit(
                params
            )
            const result = res.data.data
            profitChartData.value = result
        }
        const changeTab = (key) => {
            activeKey.value = key
            if (tabsList.value.includes(key)) {
                return
            } else {
                tabsList.value.push(key)

                if (key === '2') {
                    nextTick(() => {
                        incomeDateSearchChange()
                        // getVppDemandRecords()
                        // getProfitChartData()
                    })
                } else if (key === '3') {
                    //
                }
            }
        }
        // 异常
        const getAlarmDataFlag = ref(false)

        // 电压和温度分析
        const showAnalyze = ref(false)
        // 1
        const emitShowAnalyze = () => {
            const [startDate, endDate] = filterDate(voltageTime.value)
            const [start, end] = filterDate(electricalTime.value)
            const { stationNo } = customerDetail.value
            const deviceSn = containerList.value.find((item) => {
                return item.containerNo == tabCabinetActive.value
            }).deviceSn
            nextTick(() => {
                getBatteryVoltageRangeAndVariance(
                    {
                        stationNo,
                        deviceSn: deviceSn,
                        startDate,
                        endDate,
                    },
                    'voltage',
                    getElectricityData()
                )
                getBatteryTempRangeAndVariance(
                    {
                        stationNo,
                        deviceSn: deviceSn,
                        startDate: start,
                        endDate: end,
                    },
                    'electric',
                    getElectricityData()
                )
            })
            showAnalyze.value = true
        }
        const infoVisible = ref(false)
        const deviceInfo = ref({})
        const handleEditInfo = async () => {
            infoVisible.value = true
        }
        const onClose = async () => {
            infoVisible.value = false
            // await getEchartsData()
        }
        // 关闭弹窗，重置数据
        const onModalClose = () => {
            //
            voltageTime.value = '0'
            electricalTime.value = '0'
            tabIndexOne.value = 0
            tabIndexTwo.value = 0
        }
        const goStrategy = () => {
            router.push({
                path: '/strategy',
                query: {
                    stationNo: route.query.stationNo,
                    stationId: stationInfo.id,
                    stationOrgId: stationInfo.customerId,
                    stationName: stationInfo.stationName,
                },
            })
        }
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )

        // ai

        const switcLoading = ref(false)
        const switchVal = ref(0)
        const changeDemandResponse = async (e) => {
            //
            switcLoading.value = true
            //  接口
            let res = await apiVpp.openVppDemand({
                stationId: stationInfo.id,
                openVppDemand: switchVal.value,
            })
            if (res.data.code === 0) {
                vppInfo.value.openVppDemand = e
            } else {
                // proxy.$message.error(res.data.msg)
                vppInfo.value.openVppDemand = switchVal.value
            }
            switcLoading.value = false
        }

        const { proxy } = getCurrentInstance()
        // 开启
        const openAiModal = async () => {
            if (stationInfo.status == 3) {
                return
            }
            // 暂时流程不完善，先注释
            let res = await apiVpp.openVppStation({
                stationId: stationInfo.id,
            })
            if (res.data.data) {
                proxy.$message.success('开启成功')
            }
            await getVppInfo()
        }

        const AiOptimizeVisible = ref(false)
        const AiOptimize = async () => {
            AiOptimizeVisible.value = true
        }

        const AiOptimizeClose = () => {
            AiOptimizeVisible.value = false
        }
        const exportLoading = ref(false)
        const exportChart = async () => {
            exportLoading.value = true
            const result = await apiService.exportStationDailyChargeDetail({
                ...dateSelect.value,
                stationNo: route.query.stationNo,
                stationType: 'energy_storage_cabinet',
            })
            const { data, headers } = result
            const stationName = stationInfo.stationName
            const fileName = stationName
                ? stationName +
                  '-' +
                  (dateSelect.value.startDate || dateSelect.value.startMonth) +
                  t('to') +
                  (dateSelect.value.endDate || dateSelect.value.endMonth) +
                  t('station_chongfangdianliang') +
                  '.xlsx'
                : (dateSelect.value.startDate || dateSelect.value.startMonth) +
                  t('to') +
                  (dateSelect.value.endDate || dateSelect.value.endMonth) +
                  t('station_chongfangdianliang') +
                  '.xlsx'
            exportExcel(data, headers, fileName)
            exportLoading.value = false
        }
        const confirmExport = () => {
            //
            const arr = Object.entries(xlData.value).map(([value, key]) => ({
                ...key,
                day: value,
            }))
            let key1 = t('station_fangdiandachenglv') + '(%)'
            let key2 = t('station_zonghexiaolv') + '(%)'
            let data = arr.map((obj) => ({
                时间: obj?.day || '',
                [key1]: obj?.dischargeAchievingRate || 0,
                [key2]: obj?.comprehensiveEfficiency || 0,
            }))
            const worksheet = XLSX.utils.json_to_sheet(data)
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
            const stationName = stationInfo.stationName
            const fileName = stationName
                ? stationName +
                  '-' +
                  efficiencySelect.value.startDate +
                  t('to') +
                  efficiencySelect.value.endDate +
                  t('station_xitongxiaolv') +
                  '.xlsx'
                : efficiencySelect.value.startDate +
                  t('to') +
                  efficiencySelect.value.endDate +
                  t('station_xitongxiaolv') +
                  '.xlsx'
            XLSX.writeFile(workbook, fileName)
        }
        const confirmExportGl = () => {
            //
            const arr = Object.entries(glData.value).map(([value, key]) => ({
                ...key,
                date: value,
            }))
            let key1 = t('station_chunengdianbiaoyougonggonglv') + '(%)'
            let key2 = t('station_dianwangdianbiaoyougonggonglv') + '(%)'
            let key3 = t('station_chunengzhandianSOC') + '(%)'
            let data = arr.map((obj) => ({
                时间: obj?.date || '',
                [key1]: obj?.bmsPower || 0,
                [key2]: obj?.gridPower || 0,
                [key3]: obj?.soc || 0,
            }))
            const worksheet = XLSX.utils.json_to_sheet(data)
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
            const stationName = stationInfo.stationName
            const fileName = stationName
                ? stationName +
                  '-' +
                  powerDate.value[0] +
                  t('to') +
                  powerDate.value[1] +
                  t('station_gonglüqushifenxi') +
                  '.xlsx'
                : powerDate.value[0] +
                  t('to') +
                  powerDate.value[1] +
                  t('station_gonglüqushifenxi') +
                  '.xlsx'
            XLSX.writeFile(workbook, fileName)
        }

        const confirmExportProfit = async () => {
            exportLoading.value = true
            const res = await apiService.exportStationDailyProfitDetail({
                ...incomeDateSelect.value,
                stationNo: route.query.stationNo,
                stationType: 'energy_storage_cabinet',
            })
            const { data, headers } = res
            const stationName = stationInfo.stationName
            const fileName = stationName
                ? stationName +
                  '-' +
                  (incomeDateSelect.value.startDate ||
                      incomeDateSelect.value.startMonth) +
                  t('to') +
                  (incomeDateSelect.value.endDate ||
                      incomeDateSelect.value.endMonth) +
                  t('station_chongfangdianshouyi') +
                  '.xlsx'
                : (incomeDateSelect.value.startDate ||
                      incomeDateSelect.value.startMonth) +
                  t('to') +
                  (incomeDateSelect.value.endDate ||
                      incomeDateSelect.value.endMonth) +
                  t('station_chongfangdianshouyi') +
                  '.xlsx'
            exportExcel(data, headers, fileName)
            exportLoading.value = false
        }
        const lookDetail = (type) => {
            router.push({
                path: '/rolePage',
                query: {
                    stationNo: route.query.stationNo,
                    stationId: stationInfo.id,
                    stationOrgId: stationInfo.customerId,
                    // stationName: stationInfo.stationName,
                    type: type,
                    activeKey: '3',
                    startDate: moment(stationInfo.createTime).format(
                        'YYYY-MM-DD'
                    ),
                },
            })
        }
        const isDemoUser = computed(() => {
            return (
                store.state.user.userInfoData &&
                store.state.user.userInfoData?.userId != '1101'
            )
        })

        const handleReset = async (type, key) => {
            let params = {
                deviceType: type,
                stationId: stationInfo.id,
                containerNo: tabCabinetActive.value,
                data: {
                    [key]: 1,
                },
            }
            let res = await api.setDynamicEnvironmentSystem(params)
            if (res.data.data) {
                proxy.$message.success(t('Reset Success!'))
            }
        }
        const selectSupplierInfo = computed(() => {
            return store.state.device.selectSupplierInfo?.name
                ? store.state.device.selectSupplierInfo
                : JSON.parse(localStorage.getItem('selectSupplierInfo'))
        })
        const profitViewType = ref('chart')
        const onChangeProfitView = async (e) => {
            await incomeDateSearchChange()
        }
        const chargeFeeTableData = ref()
        // 数据3详情
        const pageInfo = ref({
            current: 1,
            size: 100,
        })
        const pageTotal = ref(0)
        const deviceDataViewType = ref('chart')

        const onChangeDeviceDataView = async () => {
            //
            if (deviceDataViewType.value == 'chart') {
                svgClickAllData(true)
                let index = containerList.value.findIndex(
                    (item) => item.containerNo == tabCabinetActive.value
                )
                productModel.value = containerList.value[index]?.productModel
            }
            if (deviceDataViewType.value == 'table') {
                await onSearchOther()
            }
        }

        const deviceType = ref('SXBLQ')
        const types = ref([
            {
                label: 'pcs',
                value: 'SXBLQ',
            },
            {
                label: t('device_type_dui'),
                value: 'DUI',
            },
            {
                label: t('device_type_cu'),
                value: 'CU',
            },
            {
                label: t('Cell'),
                value: 'DCB',
            },
            {
                label: t('device_type_dianwangdianbiao'),
                value: 'gridmeter',
            },
            {
                label: t('device_type_chunengdianbiao'),
                value: 'bmsmeter',
            },
            {
                label: t('device_type_yelengji'),
                value: 'YLJ',
            },
            {
                label: t('device_type_chushiji'),
                value: 'KT',
            },
            {
                label: t('device_type_xiaofang'),
                value: 'FIRE',
            },
            {
                label: 'DI',
                value: 'DI',
            },
        ])

        const deviceTypes = computed(() => {
            return types.value
        })
        const showField = ref([])
        const showFields = ref([])
        // 类型切换
        const tableLoading = ref(false)
        const onTypeChange = async () => {
            tableLoading.value = true
            if (pageInfo.value.current == 1) {
                await onSearchOther()
            } else {
                pageInfo.value.current = 1
            }
            tableLoading.value = false
        }
        const otherTableData = ref([])
        const allOtherTableData = ref([])
        const filedsObj = ref({})
        const getEmsDataColumn = async (type, flag) => {
            if (flag) return
            const res = await apiService.getEmsDataColumn({
                deviceType: type || deviceType.value,
                productType: 'energy_storage_cabinet',
            })
            filedsObj.value = res.data.data
        }
        const otherTableColumn = ref([])
        const onShowFieldChange = async () => {
            const newArr = allOtherTableData.value.map((item) => {
                let newItem = { ...item }
                item.temperature &&
                    typeof item.temperature == 'object' &&
                    item.temperature.forEach((temp, index) => {
                        newItem[`t${index + 1}`] = temp
                    })
                typeof item.temperature == 'object' &&
                    delete newItem.temperature
                item.voltage &&
                    typeof item.voltage == 'object' &&
                    item.voltage.forEach((vol, index) => {
                        newItem[`v${index + 1}`] = vol
                    })
                typeof item.voltage == 'object' && delete newItem.voltage
                return newItem
            })
            // tables数据根据选中的字段进行筛选
            nextTick(() => {
                otherTableData.value = newArr.map((item) => {
                    return Object.fromEntries(
                        Object.entries(item).filter(([key]) =>
                            showField.value.includes(key)
                        )
                    )
                })
                const arr = Object.entries(filedsObj.value).map(
                    ([value, label]) => ({
                        value,
                        label,
                    })
                )
                otherTableColumn.value = arr.filter((item, index) => {
                    if (showField.value.includes(item.value)) return item
                })
            })
        }
        // 日期选择  ⬇️
        const cascaderStartDate = computed(() => {
            return stationInfo.createTime || '1971-01-01'
        })
        // 这个可以根据站点详情返回的投运日期

        const detaultRangeDate = [
            moment().subtract(2, 'day').format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD'),
        ]
        const rangeDate = ref({
            startDate: detaultRangeDate[0],
            endDate: detaultRangeDate[1],
        })
        const onDateChange = async () => {
            tableLoading.value = true
            if (pageInfo.value.current == 1) {
                await onSearchOther()
            } else {
                pageInfo.value.current = 1
            }
            tableLoading.value = false
        }
        const onSearchOther = async (flag) => {
            //
            await getEmsDataColumn(
                ['gridmeter', 'bmsmeter'].includes(deviceType.value)
                    ? 'DB'
                    : deviceType.value,
                flag
            )
            const arr = Object.entries(filedsObj.value).map(
                ([value, label]) => ({
                    value,
                    label,
                })
            )
            showFields.value = arr
            showField.value = arr
                .filter((item, index) => {
                    if (index < 30) return item.value
                })
                .map((item) => item.value)
            otherTableColumn.value = arr.filter((item, index) => {
                if (index < 30) return item.value
            })
            let params = {
                stationNo: route.query.stationNo,
                containerNo: tabCabinetActive.value,
                deviceType: ['gridmeter', 'bmsmeter'].includes(deviceType.value)
                    ? 'DB'
                    : deviceType.value,
                deviceSubType: ['gridmeter', 'bmsmeter'].includes(
                    deviceType.value
                )
                    ? deviceType.value
                    : undefined,
                startDate: rangeDate.value.startDate,
                endDate: rangeDate.value.endDate,
                ...pageInfo.value,
            }
            const res = await apiService.getEmsRunDataLog(params)
            //   根据选择的字段进行筛选
            if (res.data.data) {
                pageTotal.value = res.data.data.total
                allOtherTableData.value = res.data.data.records
                await onShowFieldChange()
            } else {
                // pageInfo.value.current = current - 1
            }
            tableLoading.value = false
        }
        const handleCurrentChange = (e) => {
            onSearchOther(true)
        }
        const pageChange = () => {}
        const { themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                if (dateSelect.value && dateSelect.value.periodType == 'hour') {
                    setChargeOptions24H() // 更新充放电统计表小时纬度
                } else {
                    setChargeOptions() // 更新充放电统计表日月纬度
                }
                initDom() // 更新24小时电量表
                setEfficiencyOptions() // 系统效率表
                setPowerTrendOptions() // 功率趋势分析
                setIncomeChartOptions()
            }
        })
        return {
            deviceType,
            types,
            deviceTypes,
            showField,
            showFields,
            onTypeChange,
            onShowFieldChange,
            rangeDate,
            cascaderStartDate,
            onDateChange,
            tableLoading,
            pageInfo,
            pageTotal,
            handleCurrentChange,
            pageChange,
            otherTableData,
            otherTableColumn,

            getCompanyInfo,
            DateOptions: Object.entries(DateOptionsMap).map(([value, key]) => ({
                key,
                value,
                label: key,
            })),
            powerDate,
            tabCabinetActive,
            // descriptData,
            tabList,
            tabIndexOne,
            tabIndexTwo,
            stationInfo,
            customerDetail,
            dayjs,
            chargeStatus,
            pieceOthenData,
            unitConversion,
            alternateUnits,
            chargeSelect,
            disabledDate,
            efficiencySelect,
            efficiencyChange,
            powerRrend,
            disabledDates,
            powerDateChange,
            // powerRrendChange,
            containerList,
            showBox,
            svgClick,
            tabChange,
            runningStatus,
            bmsInfoData,
            pcsInfoData,
            onGridStatus,
            errorStatus,
            bmsMeterData,
            environmentSystem,
            patteryPackList,
            containerNums,
            voltageTime,
            electricalTime,
            voltageChange,
            voltageClick,
            electricaClick,
            pickerTime,
            pickChange,
            electricalChange,
            spinning,
            electricLoading,
            voltageLoading,
            batteryStatus,
            goRouter,
            loadingData,
            goBlack,
            isEmsControl,
            defaultImg,
            propActive,
            svgTab,

            // new
            getState,
            changeTab,
            activeKey,

            // 收益
            // 异常
            getAlarmDataFlag,

            // 分析
            showAnalyze,
            emitShowAnalyze,
            //
            infoVisible,
            handleEditInfo,
            onClose,
            onModalClose,
            deviceInfo,
            transformers,

            //
            productModel,
            DeviceClick,
            selectBox,
            selectBattery,
            loading,
            alarmData,
            cabineLoading,
            //
            goStrategy,
            transformPrice,
            transformPrices,

            // ai
            changeDemandResponse,
            switcLoading,
            openAiModal,
            vppInfo,
            completeVpp,
            dayDiff,
            AiOptimize,
            AiOptimizeVisible,
            AiOptimizeClose,
            exportChart,
            emsInfo,
            RealStrategyAndAiRecommendStrategy,
            pendingData,
            responseData,
            profitChartData,
            responseInfo,
            isDemoUser,

            // 新日期选择框
            dateSelect,
            dateSearchChange,
            handleReset,
            powerDateSelect,
            incomeDateSearchChange,
            onVisibleChange,
            onCalendarChange,
            lookDetail,
            zhCn,
            incomeDateSelect,
            confirmExport,
            confirmExportGl,
            confirmExportProfit,
            segmentTypeByHour,
            segmentTypeByHour1,
            getSegmentTypeColor,
            chargeChartData,
            segmentTypeByHourWidth,
            excelData,
            moment,
            selectSupplierInfo,
            chargeViewType,
            onChangeView,
            chargeTableData,

            onChangeProfitView,
            profitViewType,
            chargeFeeTableData,

            onChangeDeviceDataView,
            deviceDataViewType,
            exportLoading,
            updateData,
            containerRef,
            t,
            locale,
        }
    },
}
</script>

<style scoped lang="less">
.device_detail {
    padding-top: 88px;
    .border-1 {
        padding: 4px 15px;
        font-size: 14px;
        height: 32px;

        &:hover {
            color: var(--themeColor);
            border-color: var(--themeColor);
        }

        // &:focus {
        //     color: var(--themeColor) !important;
        //     border-color: var(--themeColor) !important;
        // }
    }

    .my-m-l-1 {
        margin-left: 4px;
    }

    .go-box {
        :deep(.more-icon) {
        }
        .bt-box-go {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            background-color: #fff;
            text-align: center;

            &:hover {
                background-color: var(--themeColor);
                color: #fff;
            }
        }
    }

    .blockTabsb {
        background-color: #fff;
        box-sizing: border-box;
        padding: 0 1rem;

        & :deep(.ant-tabs-ink-bar) {
            // width: 54px !important;
            height: 4px;
            background-color: var(--themeColor) !important;
            // left: 50%;
            // margin-left: 16px;
        }

        :deep(.ant-tabs-bar) {
            border-bottom: 0 !important;
            margin-bottom: 16px;
            display: flex;
            flex-direction: row-reverse;
            align-items: flex-start;
        }

        :deep(.ant-tabs-nav-container) {
            flex: 1;
        }

        :deep(.ant-tabs-extra-content) {
            line-height: 1;
        }

        :deep(.ant-tabs-content) {
            flex: 1;
        }

        :deep(.ant-tabs-tab) {
            margin: 0px;
            padding: 6px 0px;
            font-size: 14px;
            border-radius: 4px;
            // padding-right: 12px;
            margin-right: 12px;
            // background: #f5f5f5;
            color: var(--text-100);
            &:hover {
                color: var(--themeColor);
            }
        }
        & :deep(.ant-tabs-tab-active) {
            color: var(--themeColor);
            // background: #fff;
        }
    }

    .translateTabs {
        padding: 0 !important;
    }

    .tab-content {
        border-radius: 4px;
        border: 1px solid var(--border);
        overflow: hidden;

        .translate-svg {
            position: relative;
            height: 100%;

            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 80%;
                width: 1px;
                background: var(--border);
            }
        }

        .content-desc {
            height: 100%;
        }
    }

    .bg-title-t {
        background-color: var(--bg-f5);
        color: #222;
        font-size: 0.75rem;
        padding: 10px;
    }

    .origin {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: rgba(51, 190, 79, 1);
        vertical-align: middle;
        border-radius: 50%;
    }

    .bt-box {
        :deep(.translate_detail) {
            .bg-title-t {
                background-color: var(--bg-f5) !important;
            }
        }

        .bg-bt {
            background: rgba(245, 247, 247, 1);
            border-radius: 8px;
        }
    }

    .raduis-box {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: #3ecdda;
    }

    :deep(.ant-select-focused) {
        .ant-select-selector {
            border-color: var(--themeColor) !important;
            box-shadow: none !important;
        }
    }

    .w40 {
        width: 160px;
        font-size: 14px;

        :deep(.ant-select) {
            font-size: 14px;
        }

        :deep(.ant-select-selector) {
            height: 32px;
            padding: 0 11px;

            .ant-select-selection-search-input {
                height: 30px;
            }

            .ant-select-selection-item {
                line-height: 30px;
                padding-right: 18px;
            }

            .ant-select-selection-placeholder {
                line-height: 30px;
            }
        }

        :deep(.ant-select-arrow) {
            right: 11px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            font-size: 12px;
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    .range-picker {
        width: 240px;

        :deep(.ant-calendar-picker-input) {
            padding: 0px 11px;
            height: 32px;
            box-sizing: border-box;
            display: flex;

            .ant-calendar-range-picker-input {
                font-size: 14px;
                width: 90px;
                text-align: center;
            }

            .ant-calendar-picker-icon {
                right: 5px;
                // font-size: 14px;
                // top: 57%;
            }

            .ant-calendar-range-picker-separator {
                min-width: 30px;
                line-height: 30px;
                // vertical-align: middle !important;
            }
        }

        :deep(.ant-input) {
            &:focus {
                border-color: var(--themeColor);
                box-shadow: none;
            }
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    :deep(.ant-calendar-picker) {
        &:focus {
            .ant-calendar-picker-input {
                &:not(.ant-input-disabled) {
                    border-color: var(--themeColor) !important;
                    box-shadow: none;
                }
            }
        }
    }

    .my-rounded-lg {
        border-radius: 8px;
    }

    .icon-svg-box {
        width: 16px;
        height: 17.6px;
    }

    .flex-1-width {
        width: 618px;
    }
}

:deep(.topTabs .ant-tabs-nav) {
    display: none !important;
}

:deep(.ant-tabs-bar) {
    border: 0;
    margin-bottom: 0;
}

.device-tabs {
    padding: 16px 16px 0 16px;
}

:deep(.device-tabs .ant-tabs-nav) {
    display: inline-block !important;
    width: auto !important;
}

//
.detail-info {
    // background: linear-gradient(180deg, #f6f6f6 0%, #ffffff 100%);
    // background: #fff;
    border-radius: 8px;
    // border: 2px solid #ffffff;
}

// .tabs-content {
//     position: relative;
//     margin-bottom: 18px;
//     transition: all 0.3s;

//     &::after {
//         display: none;
//         content: '';
//         width: 0;
//         height: 0;
//         border-top: 9px solid var(--car-pie-border);
//         border-left: 12px solid transparent;
//         border-right: 12px solid transparent;
//         border-bottom: 1px solid transparent;
//         position: absolute;
//         left: 50%;
//         margin-left: -6px;
//         margin-top: 0px;
//         top: 100%;
//     }

//     &.active {
//         box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);

//         &::after {
//             display: block;
//         }
//     }

//     &:hover {
//         box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);
//     }
// }

.electricity {
    height: 470px;

    .voltage {
        height: 360px;
    }

    .electric {
        height: 360px;
    }

    .my-tab {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        padding: 0 10px;

        .tab-item {
            padding: 23px 0 8px 0;
            cursor: pointer;
            position: relative;
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--text-60);
        }

        .active-tab-item {
            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                width: 24px;
                left: 50%;
                transform: translateX(-50%);
                height: 3px;
                background-color: var(--themeColor);
            }
        }
    }

    .tab-select {
        display: flex;
        align-items: center;
        padding-right: 10px;
    }
}

.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(119, 155, 219, 0.1);
        border-left: 43px solid transparent;
    }
}

.cabinet {
    .se701 {
        width: 202px;
        height: 310px;
        position: relative;

        .se70-box {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 3;

            .se70-bg {
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                z-index: 2;
                cursor: pointer;
                border: 2px solid transparent;

                .block0 {
                    position: absolute;
                    z-index: 8;
                }

                .block1 {
                    width: calc(~'100% - 10px');
                    height: 18px;

                    left: 5px;
                    top: 0;
                }

                .block2 {
                    width: 5px;
                    height: 100%;

                    left: 0;
                    top: 0;
                }

                .block25 {
                    width: 4px;
                    height: 42px;
                    left: 5px;
                    top: 18px;
                }

                .block3 {
                    width: 5px;
                    height: 100%;

                    right: 0;
                    top: 0;
                }

                .block4 {
                    width: 97px;
                    height: 42px;

                    left: 99px;
                    top: 18px;
                }

                .block5 {
                    width: calc(~'100% - 10px');
                    height: 14px;

                    left: 5px;
                    top: 60px;
                }

                .block6 {
                    width: calc(~'100% - 10px');
                    height: 10px;

                    left: 5px;
                    bottom: 0;
                }

                &:hover {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }

                &.active {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);

                    .block0 {
                        background: rgba(111, 190, 206, 0.5);
                    }
                }
            }

            .pcs {
                width: 88px;
                height: 40px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 21px;
                margin-left: 12px;
                position: relative;
                z-index: 3;
                border: 2px solid transparent;

                &:hover {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }

                &.active {
                    background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }
            }

            .battery-main {
                width: 192px;
                height: 224px;
                left: 0;
                top: 0;
                cursor: pointer;
                z-index: 4;
                position: absolute;
                border: 2px solid transparent;

                &:hover {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }

                &.active {
                    background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }
            }

            .batterys {
                width: 192px;
                margin-top: 14px;
                margin-left: 5px;
                padding: 10px;
                display: flex;
                flex-wrap: wrap;
                column-gap: 10px;
                border-radius: 4px;
                position: absolute;
                z-index: 5;

                .battery {
                    width: 80px;
                    height: 32px;
                    cursor: pointer;
                    position: relative;
                    margin-bottom: 11px;
                    z-index: 6;

                    .detail {
                        width: 100%;
                        height: 100%;
                        border-radius: 4px;
                        border: 2px solid transparent;

                        &:hover {
                            // background: rgba(111, 190, 206, 0.5);
                            border: 2px solid var(--themeColor);
                        }

                        &.active {
                            background: rgba(111, 190, 206, 0.5);
                            border: 2px solid var(--themeColor);
                        }
                    }
                }
            }
        }
    }

    .se702 {
        width: 202px;
        height: 310px;
        position: relative;

        .se70-box {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 3;
        }

        .se70-bg {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 2;
            // cursor: pointer;
            border: 2px solid transparent;

            // &:hover {
            // background: rgba(111, 190, 206, 0.5);
            // border: 2px solid var(--themeColor);
            // }
            &.active {
                background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }
        }

        .ammeter {
            width: 52px;
            height: 25px;
            margin-left: 45px;
            margin-top: 33px;
            cursor: pointer;
            position: relative;
            z-index: 3;
            border: 2px solid transparent;

            &:hover {
                // background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }

            &.active {
                background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }
        }

        .dynamicRing {
            width: 140px;
            height: 180px;
            margin-top: 16px;
            margin-left: 31px;
            cursor: pointer;
            position: relative;
            z-index: 3;
            border: 2px solid transparent;
            display: flex;
            justify-content: center;
            align-items: center;

            &:hover {
                // background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }

            &.active {
                background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }
        }
    }
}

:deep(.ant-switch) {
    min-width: 40px;
    height: 24px;
}

.ant-switch-loading-icon,
.ant-switch::after {
    width: 20px;
    height: 20px;
}

:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
}
.dark .device_detail .blockTabsb {
    background-color: transparent;
}
.device {
    :deep(.el-tag) {
        background-color: var(--bg-f5);
        color: var(--input-color);
    }
    :deep(.el-select__tags-item) {
        background-color: #000; /* 背景色 */
        color: #3366ff; /* 文字颜色 */
        border-radius: 4px; /* 圆角 */
        border: 1px solid #3366ff; /* 边框 */
    }
}
.shuaxin {
    color: var(--themeColor);
}
:deep(.ant-modal-title) {
    color: var(--text-100);
}
</style>

<style lang="less">
.ant-calendar-today .ant-calendar-date {
    color: var(--themeColor);
    border-color: var(--themeColor);
}

.ant-calendar-selected-day .ant-calendar-date {
    background: var(--themeColor);
    border: 1px solid transparent;
    color: #fff;
}

.ant-calendar-date:active {
    background: var(--themeColor);
    color: #fff;
}

.ant-calendar-selected-date .ant-calendar-date {
    color: #fff;
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-in-range-cell::before {
    background: #f5f5f5;
}

// .ant-calendar-date:hover{
//     background: var(--themeColor);
//     opacity: 0.5;
// }

// .ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date{
//     background: var(--themeColor);
// }

// .ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover{
//     background: var(--themeColor);
// }

// .ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date{
//     background: var(--themeColor);

// }

.ant-calendar-picker .ant-calendar-picker-icon {
    margin-top: -8px;
}

.ant-calendar-picker .ant-calendar-picker-clear {
    display: none;
}

@keyframes changeWidth {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

.segmentTypeByHourDiv {
    width: 100%;
    animation: changeWidth 0.5s;
}

.el-select_selection {
    .el-tag {
        background-color: var(--bg-f5) !important;
        color: var(--input-color) !important;
    }
}
.el-select_selected-item {
    .el-tag {
        background-color: var(--bg-f5) !important;
        color: var(--input-color) !important;
    }
}
.el-tag {
    background-color: var(--bg-f5) !important;
    color: var(--input-color) !important;
}
:deep(.el-tag) {
    background-color: var(--bg-f5);
    color: var(--input-color);
}
.ant-modal-title {
    color: var(--text-100);
}
</style>
