<template>
    <el-popover :visible="visible" :placement="placement" :width="414">
        <p>{{ title }}</p>
        <p>{{ content }}</p>
        <div style="text-align: right; margin: 0">
        <el-button size="small" text @click="visible = false">{{$t('common_shi')}}</el-button>
        <el-button size="small" type="primary" @click="visible = false">
            {{$t('common_fou')}}
        </el-button>
        </div>
        <template #reference>
            <el-button @click="visible = true"></el-button>
        </template>
    </el-popover>
    </template>

<script setup>
import { ref } from 'vue'
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
})
const emits = defineEmits(['confirm'])
</script>

<style lang="less" scoped></style>
